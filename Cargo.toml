[package]
name = "sal"
version = "0.1.0"
edition = "2021"
authors = ["PlanetFirst <<EMAIL>>"]
description = "System Abstraction Layer - A library for easy interaction with operating system features"
repository = "https://git.threefold.info/herocode/sal"
license = "Apache-2.0"
keywords = ["system", "os", "abstraction", "platform", "filesystem"]
categories = ["os", "filesystem", "api-bindings"]
readme = "README.md"

[workspace]
members = [".", "vault"]

[dependencies]
hex = "0.4"
anyhow = "1.0.98"
base64 = "0.22.1" # Base64 encoding/decoding
cfg-if = "1.0"
chacha20poly1305 = "0.10.1" # ChaCha20Poly1305 AEAD cipher
clap = "2.34.0" # Command-line argument parsing
dirs = "6.0.0" # Directory paths
env_logger = "0.11.8" # Logger implementation
ethers = { version = "2.0.7", features = ["legacy"] } # Ethereum library
glob = "0.3.1" # For file pattern matching
jsonrpsee = "0.25.1"
k256 = { version = "0.13.4", features = [
    "ecdsa",
    "ecdh",
] } # Elliptic curve cryptography
lazy_static = "1.4.0" # For lazy initialization of static variables
libc = "0.2"
log = "0.4" # Logging facade
once_cell = "1.18.0" # Lazy static initialization
postgres = "0.19.4" # PostgreSQL client
postgres-types = "0.2.5" # PostgreSQL type conversions
r2d2 = "0.8.10"
r2d2_postgres = "0.18.2"
rand = "0.8.5" # Random number generation
redis = "0.31.0" # Redis client
regex = "1.8.1" # For regex pattern matching
rhai = { version = "1.12.0", features = ["sync"] } # Embedded scripting language
serde = { version = "1.0", features = [
    "derive",
] } # For serialization/deserialization
serde_json = "1.0" # For JSON handling
sha2 = "0.10.7" # SHA-2 hash functions
tempfile = "3.5" # For temporary file operations
tera = "1.19.0" # Template engine for text rendering
thiserror = "2.0.12" # For error handling
tokio = "1.45.0"
tokio-postgres = "0.7.8" # Async PostgreSQL client
tokio-test = "0.4.4"
uuid = { version = "1.16.0", features = ["v4"] }
reqwest = { version = "0.12.15", features = ["json"] }
urlencoding = "2.1.3"
zinit-client = "0.3.0"

# Optional features for specific OS functionality
[target.'cfg(unix)'.dependencies]
nix = "0.30.1" # Unix-specific functionality

[target.'cfg(windows)'.dependencies]
windows = { version = "0.61.1", features = [
    "Win32_Foundation",
    "Win32_System_Threading",
    "Win32_Storage_FileSystem",
] }

[dev-dependencies]
mockall = "0.13.1" # For mocking in tests
tempfile = "3.5" # For tests that need temporary files/directories
tokio = { version = "1.28", features = [
    "full",
    "test-util",
] } # For async testing

[[bin]]
name = "herodo"
path = "src/bin/herodo.rs"
