#!/bin/bash
set -e

# Change to directory where this script is located
cd "$(dirname "${BASH_SOURCE[0]}")"

rm -f ./target/debug/herodo

# Build the herodo project
echo "Building herodo..."
cargo build --bin herodo
# cargo build --release --bin herodo

# Check if the build was successful
if [ $? -ne 0 ]; then
  echo "Build failed. Please check the error messages."
  exit 1
fi

# Echo a success message
echo "Build successful!"

mkdir -p ~/hero/bin/
cp target/debug/herodo ~/hero/bin/herodo

# Check if a script name was provided
if [ $# -eq 1 ]; then
  echo "Running specified test: $1"
  
  # Check if the script exists in src/rhaiexamples/
  if [ -f "src/rhaiexamples/$1.rhai" ]; then
    herodo "src/rhaiexamples/$1.rhai"
  # Check if the script exists in src/herodo/scripts/
  elif [ -f "src/herodo/scripts/$1.rhai" ]; then
    herodo "src/herodo/scripts/$1.rhai"
  else
    echo "Error: Script $1.rhai not found in src/rhaiexamples/ or src/herodo/scripts/"
    exit 1
  fi
  
  exit 0
fi
