# Text Module Tests

This document describes the test scripts for the Text module in the SAL library. These tests verify the functionality of the Text module's text manipulation, normalization, replacement, and template rendering capabilities.

## Test Structure

The tests are organized into four main scripts:

1. **Text Indentation** (`01_text_indentation.rhai`): Tests for the `dedent` and `prefix` functions.
2. **Filename and Path Normalization** (`02_name_path_fix.rhai`): Tests for the `name_fix` and `path_fix` functions.
3. **Text Replacement** (`03_text_replacer.rhai`): Tests for the `TextReplacer` class and its methods.
4. **Template Rendering** (`04_template_builder.rhai`): Tests for the `TemplateBuilder` class and its methods.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/text/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/text/01_text_indentation.rhai
```

## Test Details

### Text Indentation Test

The text indentation test (`01_text_indentation.rhai`) verifies the following functions:

- `dedent`: Removes common leading whitespace from multiline strings
  - Tests basic indentation removal
  - Tests mixed indentation handling
  - Tests preservation of empty lines
  - Tests handling of text without indentation
  - Tests single line indentation removal

- `prefix`: Adds a specified prefix to each line of a multiline string
  - Tests basic prefix addition
  - Tests empty prefix handling
  - Tests prefix addition to empty lines
  - Tests prefix addition to single line
  - Tests non-space prefix addition

- Combination of `dedent` and `prefix` functions

### Filename and Path Normalization Test

The filename and path normalization test (`02_name_path_fix.rhai`) verifies the following functions:

- `name_fix`: Normalizes filenames
  - Tests basic name fixing (spaces to underscores, lowercase conversion)
  - Tests special character handling
  - Tests multiple special character handling
  - Tests non-ASCII character removal
  - Tests uppercase conversion

- `path_fix`: Applies `name_fix` to the filename portion of a path
  - Tests paths ending with `/` (directories)
  - Tests single filename handling
  - Tests path with filename handling
  - Tests relative path handling
  - Tests path with special characters in filename

### Text Replacement Test

The text replacement test (`03_text_replacer.rhai`) verifies the following functions:

- `TextReplacer` with simple replacements
  - Tests basic replacement
  - Tests multiple replacements

- `TextReplacer` with regex replacements
  - Tests basic regex replacement
  - Tests case-insensitive regex replacement

- `TextReplacer` with file operations
  - Tests `replace_file` (read file, apply replacements, return result)
  - Tests `replace_file_to` (read file, apply replacements, write to new file)
  - Tests `replace_file_in_place` (read file, apply replacements, write back to same file)

### Template Rendering Test

The template rendering test (`04_template_builder.rhai`) verifies the following functions:

- `TemplateBuilder` with file template
  - Tests basic template with string variable
  - Tests template with multiple variables of different types
  - Tests template with array variable
  - Tests template with map variable

- `TemplateBuilder` with file operations
  - Tests template from file
  - Tests `render_to_file` (render template, write to file)

Note: The `template_builder_open` function expects a file path, not a string template. The test creates template files on disk for testing.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Contains simplified versions of each test
2. Runs each test in a try/catch block to handle errors
3. Catches and reports any errors
4. Provides a summary of passed and failed tests

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/text` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the Text module:

1. Use the `assert_true` and `assert_eq` functions to verify expected behavior
2. Print clear messages about what's being tested
3. Clean up any temporary files or directories created during testing
4. Handle errors gracefully
5. Make tests independent of each other
6. Keep tests focused on specific functionality
