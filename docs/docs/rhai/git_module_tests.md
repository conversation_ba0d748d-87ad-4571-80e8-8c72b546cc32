# Git Module Tests

This document describes the test scripts for the Git module in the SAL library. These tests verify the functionality of the Git module's repository management and Git operations.

## Test Structure

The tests are organized into two main scripts:

1. **Basic Git Operations** (`01_git_basic.rhai`): Tests basic Git functionality like creating a GitTree, listing repositories, finding repositories, and cloning repositories.
2. **Git Repository Operations** (`02_git_operations.rhai`): Tests Git operations like pull, reset, commit, and push.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/git/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/git/01_git_basic.rhai
```

## Test Details

### Basic Git Operations Test

The basic Git operations test (`01_git_basic.rhai`) verifies the following functions:

- `git_tree_new`: Creating a GitTree
- `list`: Listing repositories in a GitTree
- `find`: Finding repositories matching a pattern
- `get`: Getting or cloning a repository
- `path`: Getting the path of a repository
- `has_changes`: Checking if a repository has changes

The test creates a temporary directory, performs operations on it, and then cleans up after itself.

### Git Repository Operations Test

The Git repository operations test (`02_git_operations.rhai`) verifies the following functions:

- `pull`: Pulling changes from a remote repository
- `reset`: Resetting local changes
- `commit`: Committing changes (method existence only)
- `push`: Pushing changes to a remote repository (method existence only)

Note: The test does not actually commit or push changes to avoid modifying remote repositories. It only verifies that the methods exist and can be called.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Contains simplified versions of each test
2. Runs each test in a try/catch block to handle errors
3. Catches and reports any errors
4. Provides a summary of passed and failed tests

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/git` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the Git module:

1. Always clean up temporary files and directories
2. Use assertions to verify expected behavior
3. Print clear messages about what's being tested
4. Handle errors gracefully
5. Make tests independent of each other
6. Avoid tests that modify remote repositories
7. Keep tests focused on specific functionality
