# Process Module Tests

This document describes the test scripts for the Process module in the SAL library. These tests verify the functionality of the Process module's command execution and process management features.

## Test Structure

The tests are organized into two main scripts:

1. **Command Execution** (`01_command_execution.rhai`): Tests command execution functions like `run()` and `which()`.
2. **Process Management** (`02_process_management.rhai`): Tests process management functions like `process_list()` and `process_get()`.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/process/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/process/01_command_execution.rhai
```

## Test Details

### Command Execution Test

The command execution test (`01_command_execution.rhai`) verifies the following functions:

- `run()`: Running shell commands
- `run().do()`: Executing commands and capturing output
- `run().silent()`: Running commands without displaying output
- `run().ignore_error()`: Running commands that might fail without throwing errors
- `which()`: Finding the path of an executable

The test runs various commands and verifies their output and exit status.

### Process Management Test

The process management test (`02_process_management.rhai`) verifies the following functions:

- `process_list()`: Listing running processes
- `process_get()`: Getting information about a specific process
- Process properties: Accessing process information like PID, name, CPU usage, and memory usage

The test lists running processes and verifies that their properties are accessible.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Contains simplified versions of each test
2. Runs each test in a try/catch block to handle errors
3. Catches and reports any errors
4. Provides a summary of passed and failed tests

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/process` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the Process module:

1. Use assertions to verify expected behavior
2. Print clear messages about what's being tested
3. Handle errors gracefully
4. Make tests independent of each other
5. Avoid tests that could disrupt the system (e.g., killing important processes)
6. Keep tests focused on specific functionality
7. Clean up any resources created during testing
