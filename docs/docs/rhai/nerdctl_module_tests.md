# Nerdctl Module Tests

This document describes the test scripts for the Nerdctl module in the SAL library. These tests verify the functionality of the Nerdctl module's container and image operations.

## Test Structure

The tests are organized into three main scripts:

1. **Container Operations** (`01_container_operations.rhai`): Tests for basic container operations like creating, running, executing commands, and removing containers.
2. **Image Operations** (`02_image_operations.rhai`): Tests for image-related operations like pulling, tagging, listing, building, and removing images.
3. **Container Builder Pattern** (`03_container_builder.rhai`): Tests for the Container Builder pattern, which provides a fluent interface for configuring and running containers.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/nerdctl/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/nerdctl/01_container_operations.rhai
```

## Test Details

### Container Operations Test

The Container Operations test (`01_container_operations.rhai`) verifies the following functions:

- `nerdctl_container_new`: Creating a new Container
- Container properties: `name`, `container_id`, `image`, `detach`
- `with_image`: Setting the container image
- `with_detach`: Setting detach mode
- `with_env` and `with_envs`: Setting environment variables
- `with_port` and `with_ports`: Setting port mappings
- `with_volume`: Setting volume mounts
- `with_cpu_limit` and `with_memory_limit`: Setting resource limits
- `run`: Running the container
- `exec`: Executing commands in the container
- `logs`: Getting container logs
- `stop`: Stopping the container
- `remove`: Removing the container

### Image Operations Test

The Image Operations test (`02_image_operations.rhai`) verifies the following functions:

- `nerdctl_image_pull`: Pulling images from registries
- `nerdctl_images`: Listing images
- `nerdctl_image_tag`: Tagging images
- `nerdctl_image_build`: Building images from Dockerfiles
- `nerdctl_run_with_name`: Running containers from images
- `nerdctl_stop` and `nerdctl_remove`: Stopping and removing containers
- `nerdctl_image_remove`: Removing images

The test creates a temporary directory with a Dockerfile for testing the build functionality.

### Container Builder Pattern Test

The Container Builder Pattern test (`03_container_builder.rhai`) verifies the following functions:

- `nerdctl_container_from_image`: Creating a container from an image
- `reset`: Resetting container configuration
- `with_detach`: Setting detach mode
- `with_ports`: Setting multiple port mappings
- `with_volumes`: Setting multiple volume mounts
- `with_envs`: Setting multiple environment variables
- `with_network`: Setting network
- `with_cpu_limit` and `with_memory_limit`: Setting resource limits
- `run`: Running the container
- `exec`: Executing commands in the container
- `stop`: Stopping the container
- `remove`: Removing the container

The test also verifies that environment variables and volume mounts work correctly by writing and reading files between the container and the host.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Checks if nerdctl is available before running tests
2. Skips tests if nerdctl is not available
3. Contains simplified versions of each test
4. Runs each test in a try/catch block to handle errors
5. Catches and reports any errors
6. Provides a summary of passed, failed, and skipped tests

## Nerdctl Requirements

These tests require the nerdctl tool to be installed and available in the system's PATH. The tests will check for nerdctl's availability and skip the tests if it's not found, rather than failing.

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/nerdctl` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the Nerdctl module:

1. Always check if nerdctl is available before running tests
2. Use unique names for containers and images to avoid conflicts
3. Clean up any containers, images, or files created during testing
4. Use assertions to verify expected behavior
5. Print clear messages about what's being tested
6. Handle errors gracefully
7. Make tests independent of each other
8. Keep tests focused on specific functionality
