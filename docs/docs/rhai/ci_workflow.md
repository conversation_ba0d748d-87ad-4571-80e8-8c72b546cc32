# Continuous Integration for Rhai Tests

This document describes the continuous integration (CI) workflow for running Rhai tests in the SAL library.

## GitHub Actions Workflow

The SAL project includes a GitHub Actions workflow that automatically runs all Rhai tests whenever changes are made to relevant files. This ensures that the Rhai integration continues to work correctly as the codebase evolves.

### Workflow File

The workflow is defined in `.github/workflows/rhai-tests.yml`.

### Trigger Events

The workflow runs automatically when:

1. Changes are pushed to the `main` or `master` branch that affect:
   - Rhai test scripts (`src/rhai_tests/**`)
   - Rhai module code (`src/rhai/**`)
   - Git module code (`src/git/**`)
   - OS module code (`src/os/**`)
   - The test runner script (`run_rhai_tests.sh`)
   - The workflow file itself (`.github/workflows/rhai-tests.yml`)

2. A pull request is opened or updated that affects the same files.

3. The workflow is manually triggered using the GitHub Actions interface.

### Workflow Steps

The workflow performs the following steps:

1. **Checkout Code**: Checks out the repository code.
2. **Set up Rust**: Installs the Rust toolchain.
3. **Cache Dependencies**: Caches Rust dependencies to speed up builds.
4. **Build herodo**: Builds the `herodo` binary used to run Rhai scripts.
5. **Install Dependencies**: Installs system dependencies like Git and curl.
6. **Run Rhai Tests**: Runs the `run_rhai_tests.sh` script to execute all Rhai tests.
7. **Check for Failures**: Verifies that all tests passed.

### Test Results

The workflow will fail if any Rhai test fails. This prevents changes that break the Rhai integration from being merged.

## Local Testing

Before pushing changes, you can run the same tests locally using the `run_rhai_tests.sh` script:

```bash
./run_rhai_tests.sh
```

This will produce the same test results as the CI workflow, allowing you to catch and fix issues before pushing your changes.

## Logs

The test runner script creates a log file (`run_rhai_tests.log`) that contains the output of all tests. This log is used by the CI workflow to check for test failures.

## Adding New Tests

When adding new tests, make sure they are included in the appropriate module's test runner script (`run_all_tests.rhai`). The CI workflow will automatically run the new tests.

## Troubleshooting

If the CI workflow fails, check the GitHub Actions logs for details. Common issues include:

1. **Missing Dependencies**: Ensure all required dependencies are installed.
2. **Test Failures**: Fix any failing tests.
3. **Build Errors**: Fix any errors in the Rust code.

If you need to modify the workflow, edit the `.github/workflows/rhai-tests.yml` file.
