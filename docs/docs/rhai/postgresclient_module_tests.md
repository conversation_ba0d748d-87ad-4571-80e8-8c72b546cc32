# PostgreSQL Client Module Tests

The PostgreSQL client module provides functions for connecting to and interacting with PostgreSQL databases. These tests verify the functionality of the module.

## PostgreSQL Client Features

The PostgreSQL client module provides the following features:

1. **Basic PostgreSQL Operations**: Execute queries, fetch results, etc.
2. **Connection Management**: Automatic connection handling and reconnection
3. **Builder Pattern for Configuration**: Flexible configuration with authentication support
4. **PostgreSQL Installer**: Install and configure PostgreSQL using nerdctl
5. **Database Management**: Create databases and execute SQL scripts

## Prerequisites

For basic PostgreSQL operations:
- PostgreSQL server must be running and accessible
- Environment variables should be set for connection details:
  - `POSTGRES_HOST`: PostgreSQL server host (default: localhost)
  - `POSTGRES_PORT`: PostgreSQL server port (default: 5432)
  - `POSTGRES_USER`: PostgreSQL username (default: postgres)
  - `POSTGRES_PASSWORD`: PostgreSQL password
  - `POSTGRES_DB`: PostgreSQL database name (default: postgres)

For PostgreSQL installer:
- nerdctl must be installed and working
- Docker images must be accessible
- Sufficient permissions to create and manage containers

## Test Files

### 01_postgres_connection.rhai

Tests basic PostgreSQL connection and operations:

- Connecting to PostgreSQL
- Pinging the server
- Creating a table
- Inserting data
- Querying data
- Dropping a table
- Resetting the connection

### 02_postgres_installer.rhai

Tests PostgreSQL installer functionality:

- Installing PostgreSQL using nerdctl
- Creating a database
- Executing SQL scripts
- Checking if PostgreSQL is running

### run_all_tests.rhai

Runs all PostgreSQL client module tests and provides a summary of the results.

## Running the Tests

You can run the tests using the `herodo` command:

```bash
herodo --path src/rhai_tests/postgresclient/run_all_tests.rhai
```

Or run individual tests:

```bash
herodo --path src/rhai_tests/postgresclient/01_postgres_connection.rhai
```

## Available Functions

### Connection Functions

- `pg_connect()`: Connect to PostgreSQL using environment variables
- `pg_ping()`: Ping the PostgreSQL server to check if it's available
- `pg_reset()`: Reset the PostgreSQL client connection

### Query Functions

- `pg_execute(query)`: Execute a query and return the number of affected rows
- `pg_query(query)`: Execute a query and return the results as an array of maps
- `pg_query_one(query)`: Execute a query and return a single row as a map

### Installer Functions

- `pg_install(container_name, version, port, username, password)`: Install PostgreSQL using nerdctl
- `pg_create_database(container_name, db_name)`: Create a new database in PostgreSQL
- `pg_execute_sql(container_name, db_name, sql)`: Execute a SQL script in PostgreSQL
- `pg_is_running(container_name)`: Check if PostgreSQL is running

## Authentication Support

The PostgreSQL client module will support authentication using the builder pattern in a future update.

The backend implementation is ready, but the Rhai bindings are still in development.

When implemented, the builder pattern will support the following configuration options:

- Host: Set the PostgreSQL host
- Port: Set the PostgreSQL port
- User: Set the PostgreSQL username
- Password: Set the PostgreSQL password
- Database: Set the PostgreSQL database name
- Application name: Set the application name
- Connection timeout: Set the connection timeout in seconds
- SSL mode: Set the SSL mode

## Example Usage

### Basic PostgreSQL Operations

```rust
// Connect to PostgreSQL
if (pg_connect()) {
    print("Connected to PostgreSQL!");

    // Create a table
    let create_table_query = "CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, name TEXT)";
    pg_execute(create_table_query);

    // Insert data
    let insert_query = "INSERT INTO test_table (name) VALUES ('test')";
    pg_execute(insert_query);

    // Query data
    let select_query = "SELECT * FROM test_table";
    let results = pg_query(select_query);

    // Process results
    for (result in results) {
        print(`ID: ${result.id}, Name: ${result.name}`);
    }

    // Clean up
    let drop_query = "DROP TABLE test_table";
    pg_execute(drop_query);
}
```

### PostgreSQL Installer

```rust
// Install PostgreSQL
let container_name = "my-postgres";
let postgres_version = "15";
let postgres_port = 5432;
let postgres_user = "myuser";
let postgres_password = "mypassword";

if (pg_install(container_name, postgres_version, postgres_port, postgres_user, postgres_password)) {
    print("PostgreSQL installed successfully!");

    // Create a database
    let db_name = "mydb";
    if (pg_create_database(container_name, db_name)) {
        print(`Database '${db_name}' created successfully!`);

        // Execute a SQL script
        let create_table_sql = `
            CREATE TABLE users (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL
            );
        `;

        let result = pg_execute_sql(container_name, db_name, create_table_sql);
        print("Table created successfully!");

        // Insert data
        let insert_sql = "#
            INSERT INTO users (name, email) VALUES
            ('John Doe', '<EMAIL>'),
            ('Jane Smith', '<EMAIL>');
        #";

        result = pg_execute_sql(container_name, db_name, insert_sql);
        print("Data inserted successfully!");

        // Query data
        let query_sql = "SELECT * FROM users;";
        result = pg_execute_sql(container_name, db_name, query_sql);
        print(`Query result: ${result}`);
    }
}
```
