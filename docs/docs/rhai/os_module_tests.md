# OS Module Tests

This document describes the test scripts for the OS module in the SAL library. These tests verify the functionality of the OS module's file system operations, download capabilities, and package management features.

## Test Structure

The tests are organized into three main scripts:

1. **File Operations** (`01_file_operations.rhai`): Tests file system operations like creating, reading, writing, and manipulating files and directories.
2. **Download Operations** (`02_download_operations.rhai`): Tests downloading files from the internet and related operations.
3. **Package Operations** (`03_package_operations.rhai`): Tests package management functionality.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency on the `run_script` function.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
# Assume that you have the herodo binary/built into your system
herodo --path src/rhai_tests/os/run_all_tests.rhai
```

To run individual test scripts:

```bash
# Assume that you have the herodo binary/built into your system
herodo --path src/rhai_tests/os/01_file_operations.rhai
```

## Test Details

### File Operations Test

The file operations test (`01_file_operations.rhai`) verifies the following functions:

- `mkdir`: Creating directories
- `file_write`: Writing content to files
- `file_read`: Reading content from files
- `file_size`: Getting file size
- `file_write_append`: Appending content to files
- `copy`: Copying files
- `mv`: Moving files
- `find_file`: Finding a single file matching a pattern
- `find_files`: Finding multiple files matching a pattern
- `find_dir`: Finding a single directory matching a pattern
- `find_dirs`: Finding multiple directories matching a pattern
- `chdir`: Changing the current working directory
- `rsync`: Synchronizing directories
- `delete`: Deleting files and directories
- `exist`: Checking if files or directories exist

The test creates a temporary directory structure, performs operations on it, and then cleans up after itself.

### Download Operations Test

The download operations test (`02_download_operations.rhai`) verifies the following functions:

- `which`: Checking if a command exists in the system PATH
- `cmd_ensure_exists`: Ensuring commands exist
- `download_file`: Downloading a file from a URL
- `chmod_exec`: Making a file executable

The test downloads a small file from GitHub, verifies its content, and then cleans up.

### Package Operations Test

The package operations test (`03_package_operations.rhai`) verifies the following functions:

- `package_platform`: Getting the current platform
- `package_set_debug`: Setting debug mode for package operations
- `package_is_installed`: Checking if a package is installed
- `package_search`: Searching for packages
- `package_list`: Listing installed packages

Note: The test does not verify `package_install`, `package_remove`, `package_update`, or `package_upgrade` as these require root privileges and could modify the system state.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Contains simplified versions of each test
2. Runs each test in a try/catch block to handle errors
3. Catches and reports any errors
4. Provides a summary of passed and failed tests

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/os` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the OS module:

1. Always clean up temporary files and directories
2. Use assertions to verify expected behavior
3. Print clear messages about what's being tested
4. Handle errors gracefully
5. Make tests independent of each other
6. Avoid tests that require root privileges when possible
7. Keep tests focused on specific functionality
