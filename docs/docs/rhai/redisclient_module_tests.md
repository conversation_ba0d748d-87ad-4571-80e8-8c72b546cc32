# Redis Client Module Tests

This document describes the test scripts for the Redis client module in the SAL library. These tests verify the functionality of the Redis client module's connection management and Redis operations.

## Redis Client Features

The Redis client module provides the following features:

1. **Basic Redis Operations**: SET, GET, DEL, etc.
2. **Hash Operations**: HSET, HGET, HGETALL, HDEL
3. **List Operations**: RPUSH, LPUSH, LLEN, LRANGE
4. **Connection Management**: Automatic connection handling and reconnection
5. **<PERSON>uilder Pattern for Configuration**: Flexible configuration with authentication support

## Test Structure

The tests are organized into two main scripts:

1. **Redis Connection** (`01_redis_connection.rhai`): Tests basic Redis connection and simple operations like PING, SET, GET, and DEL.
2. **Redis Operations** (`02_redis_operations.rhai`): Tests more advanced Redis operations like hash operations (HSET, HGET, HGETALL, HDEL) and list operations (RPUSH, LLEN, LRANGE).

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/redisclient/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/redisclient/01_redis_connection.rhai
```

## Test Details

### Redis Connection Test

The Redis connection test (`01_redis_connection.rhai`) verifies the following functions:

- `redis_ping`: Checking if the Redis server is available
- `redis_set`: Setting a key-value pair
- `redis_get`: Getting a value by key
- `redis_del`: Deleting a key

The test creates a temporary key, performs operations on it, and then cleans up after itself.

### Redis Operations Test

The Redis operations test (`02_redis_operations.rhai`) verifies the following functions:

- Hash operations:
  - `redis_hset`: Setting a field in a hash
  - `redis_hget`: Getting a field from a hash
  - `redis_hgetall`: Getting all fields and values from a hash
  - `redis_hdel`: Deleting a field from a hash

- List operations:
  - `redis_rpush`: Adding elements to a list
  - `redis_llen`: Getting the length of a list
  - `redis_lrange`: Getting a range of elements from a list

The test creates temporary keys with a unique prefix, performs operations on them, and then cleans up after itself.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Checks if Redis is available before running tests
2. Skips tests if Redis is not available
3. Contains simplified versions of each test
4. Runs each test in a try/catch block to handle errors
5. Catches and reports any errors
6. Provides a summary of passed, failed, and skipped tests

## Redis Server Requirements

These tests require a Redis server to be running and accessible. The tests will attempt to connect to Redis using the following strategy:

1. First, try to connect via Unix socket at `$HOME/hero/var/myredis.sock`
2. If that fails, try to connect via TCP to `127.0.0.1` on the default Redis port (6379)

If no Redis server is available, the tests will be skipped rather than failing.

## Authentication Support

The Redis client module will support authentication using the builder pattern in a future update.

The backend implementation is ready, but the Rhai bindings are still in development.

When implemented, the builder pattern will support the following configuration options:

- Host: Set the Redis host
- Port: Set the Redis port
- Database: Set the Redis database number
- Username: Set the Redis username (Redis 6.0+)
- Password: Set the Redis password
- TLS: Enable/disable TLS
- Unix socket: Enable/disable Unix socket
- Socket path: Set the Unix socket path
- Connection timeout: Set the connection timeout in seconds

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/redisclient` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the Redis client module:

1. Always check if Redis is available before running tests
2. Use a unique prefix for test keys to avoid conflicts
3. Clean up any keys created during testing
4. Use assertions to verify expected behavior
5. Print clear messages about what's being tested
6. Handle errors gracefully
7. Make tests independent of each other
8. Keep tests focused on specific functionality
