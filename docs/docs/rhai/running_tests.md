# Running Rhai Tests

This document describes how to run the Rhai tests for the SAL library.

## Test Structure

The Rhai tests are organized by module in the `src/rhai_tests` directory:

- `src/rhai_tests/os/`: Tests for the OS module
- `src/rhai_tests/git/`: Tests for the Git module

Each module directory contains:
- Individual test scripts (e.g., `01_file_operations.rhai`)
- A test runner script (`run_all_tests.rhai`) that runs all tests for that module

## Running Tests

### Running All Tests

To run all Rhai tests across all modules, use the provided shell script:

```bash
./run_rhai_tests.sh
```

This script:
1. Finds all test runner scripts in the `src/rhai_tests` directory
2. Runs each test runner
3. Reports the results for each module
4. Provides a summary of all test results

The script will exit with code 0 if all tests pass, or code 1 if any tests fail.

### Running Tests for a Specific Module

To run tests for a specific module, use the `herodo` command with the module's test runner:

```bash
herodo --path src/rhai_tests/os/run_all_tests.rhai
```

### Running Individual Tests

To run a specific test, use the `herodo` command with the test script:

```bash
herodo --path src/rhai_tests/os/01_file_operations.rhai
```

## Test Output

The test output includes:
- Information about what's being tested
- Success or failure messages for each test
- A summary of test results

Successful tests are indicated with a checkmark (✓), while failed tests show an error message.

## Adding New Tests

When adding new tests:

1. Create a new test script in the appropriate module directory
2. Update the module's test runner script to include the new test
3. Update the module's documentation to describe the new test

The `run_rhai_tests.sh` script will automatically find and run the new tests as long as they're included in a module's test runner script.

## Troubleshooting

If tests fail, check the following:

1. Make sure the `herodo` binary is in your PATH
2. Verify that the test scripts have the correct permissions
3. Check for any dependencies required by the tests (e.g., `git` for Git module tests)
4. Look for specific error messages in the test output
