# RFS Module Tests

This document describes the test scripts for the RFS (Remote File System) module in the SAL library. These tests verify the functionality of the RFS module's mount operations and filesystem layer management.

## Test Structure

The tests are organized into two main scripts:

1. **Mount Operations** (`01_mount_operations.rhai`): Tests for mounting, listing, and unmounting filesystems.
2. **Filesystem Layer Operations** (`02_filesystem_layer_operations.rhai`): Tests for packing, unpacking, listing, and verifying filesystem layers.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/rfs/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/rfs/01_mount_operations.rhai
```

## Test Details

### Mount Operations Test

The Mount Operations test (`01_mount_operations.rhai`) verifies the following functions:

- `rfs_mount`: Mounting a filesystem
  - Tests mounting a local directory with options
  - Verifies mount properties (ID, source, target, type)

- `rfs_list_mounts`: Listing mounted filesystems
  - Tests listing all mounts
  - Verifies that the mounted filesystem is in the list

- `rfs_get_mount_info`: Getting information about a mounted filesystem
  - Tests getting information about a specific mount
  - Verifies that the mount information is correct

- `rfs_unmount`: Unmounting a specific filesystem
  - Tests unmounting a specific mount
  - Verifies that the mount is no longer available

- `rfs_unmount_all`: Unmounting all filesystems
  - Tests unmounting all mounts
  - Verifies that no mounts remain after the operation

The test also verifies that files in the mounted filesystem are accessible and have the correct content.

### Filesystem Layer Operations Test

The Filesystem Layer Operations test (`02_filesystem_layer_operations.rhai`) verifies the following functions:

- `rfs_pack`: Packing a directory into a filesystem layer
  - Tests packing a directory with files and subdirectories
  - Verifies that the output file is created

- `rfs_list_contents`: Listing the contents of a filesystem layer
  - Tests listing the contents of a packed filesystem layer
  - Verifies that the list includes all expected files

- `rfs_verify`: Verifying a filesystem layer
  - Tests verifying a packed filesystem layer
  - Verifies that the layer is valid

- `rfs_unpack`: Unpacking a filesystem layer
  - Tests unpacking a filesystem layer to a directory
  - Verifies that all files are unpacked correctly with the right content

The test creates a directory structure with files, packs it into a filesystem layer, and then unpacks it to verify the integrity of the process.

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Checks if RFS is available before running tests
2. Skips tests if RFS is not available
3. Contains simplified versions of each test
4. Runs each test in a try/catch block to handle errors
5. Catches and reports any errors
6. Provides a summary of passed, failed, and skipped tests

## RFS Requirements

These tests require the RFS tool to be installed and available in the system's PATH. The tests will check for RFS's availability and skip the tests if it's not found, rather than failing.

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/rfs` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the RFS module:

1. Always check if RFS is available before running tests
2. Clean up any mounts before and after testing
3. Use unique names for test directories and files to avoid conflicts
4. Clean up any files or directories created during testing
5. Use assertions to verify expected behavior
6. Print clear messages about what's being tested
7. Handle errors gracefully
8. Make tests independent of each other
9. Keep tests focused on specific functionality
