# Buildah Module Tests

This document describes the test scripts for the Buildah module in the SAL library. These tests verify the functionality of the Buildah module's container and image operations.

## Test Structure

The tests are organized into three main scripts:

1. **Builder Pattern** (`01_builder_pattern.rhai`): Tests for the Builder pattern, including creating containers, running commands, and working with container content.
2. **Image Operations** (`02_image_operations.rhai`): Tests for image-related operations like pulling, tagging, listing, and removing images.
3. **Container Operations** (`03_container_operations.rhai`): Tests for container-related operations like configuration, isolation, and content management.

Additionally, there's a runner script (`run_all_tests.rhai`) that executes all tests and reports results. The runner script contains simplified versions of the individual tests to avoid dependency issues.

## Running the Tests

To run all tests, execute the following command from the project root:

```bash
herodo --path src/rhai_tests/buildah/run_all_tests.rhai
```

To run individual test scripts:

```bash
herodo --path src/rhai_tests/buildah/01_builder_pattern.rhai
```

## Test Details

### Builder Pattern Test

The Builder Pattern test (`01_builder_pattern.rhai`) verifies the following functions:

- `bah_new`: Creating a new Builder with a container from a specified image
- Builder properties: `container_id`, `name`, `image`, `debug_mode`
- `run`: Running commands in the container
- `write_content`: Writing content to files in the container
- `read_content`: Reading content from files in the container
- `set_entrypoint`: Setting the container's entrypoint
- `set_cmd`: Setting the container's command
- `add`: Adding files to the container
- `copy`: Copying files to the container
- `commit`: Committing the container to an image
- `remove`: Removing the container
- `images`: Listing images
- `image_remove`: Removing images

### Image Operations Test

The Image Operations test (`02_image_operations.rhai`) verifies the following functions:

- `image_pull`: Pulling images from registries
- `image_tag`: Tagging images
- `images`: Listing images
- `build`: Building images from Dockerfiles
- `image_remove`: Removing images

The test creates a temporary directory with a Dockerfile for testing the build functionality.

### Container Operations Test

The Container Operations test (`03_container_operations.rhai`) verifies the following functions:

- `reset`: Resetting a Builder by removing its container
- `config`: Configuring container properties
- `run_with_isolation`: Running commands with isolation
- Content operations: Creating and executing scripts in the container
- `commit` with options: Committing a container with additional configuration

## Test Runner

The test runner script (`run_all_tests.rhai`) provides a framework for executing all tests and reporting results. It:

1. Checks if Buildah is available before running tests
2. Skips tests if Buildah is not available
3. Contains simplified versions of each test
4. Runs each test in a try/catch block to handle errors
5. Catches and reports any errors
6. Provides a summary of passed, failed, and skipped tests

## Buildah Requirements

These tests require the Buildah tool to be installed and available in the system's PATH. The tests will check for Buildah's availability and skip the tests if it's not found, rather than failing.

## Adding New Tests

To add a new test:

1. Create a new Rhai script in the `src/rhai_tests/buildah` directory
2. Add a new test section to the `run_all_tests.rhai` script
3. Update this documentation to include information about the new test

## Best Practices for Writing Tests

When writing tests for the Buildah module:

1. Always check if Buildah is available before running tests
2. Use unique names for containers and images to avoid conflicts
3. Clean up any containers, images, or files created during testing
4. Use assertions to verify expected behavior
5. Print clear messages about what's being tested
6. Handle errors gracefully
7. Make tests independent of each other
8. Keep tests focused on specific functionality
