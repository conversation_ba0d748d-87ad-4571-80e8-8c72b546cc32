# Rhai Scripting in SAL

This documentation covers the Rhai scripting integration in the SAL (System Abstraction Layer) library.

## Overview

SAL provides integration with the [Rhai scripting language](https://rhai.rs/), allowing you to use SAL's functionality in scripts. This enables automation of system tasks, testing, and more complex operations without having to write Rust code.

## Modules

SAL exposes the following modules to Rhai scripts:

- [OS Module](os_module_tests.md): File system operations, downloads, and package management
- Process Module: Process management and command execution
- Git Module: Git repository operations
- Text Module: Text processing utilities
- Buildah Module: Container image building
- Nerdctl Module: Container runtime operations
- RFS Module: Remote file system operations
- Redis Client Module: Redis database connection and operations
- PostgreSQL Client Module: PostgreSQL database connection and operations

## Running Rhai Scripts

You can run Rhai scripts using the `herodo` binary:

```bash
herodo --path path/to/script.rhai
```

## Testing

SAL includes test scripts for verifying the functionality of its Rhai integration. These tests are located in the `src/rhai_tests` directory and are organized by module.

- [OS Module Tests](os_module_tests.md): Tests for file system, download, and package management operations
- [Git Module Tests](git_module_tests.md): Tests for Git repository management and operations
- [Process Module Tests](process_module_tests.md): Tests for command execution and process management
- [Redis Client Module Tests](redisclient_module_tests.md): Tests for Redis connection and operations
- [PostgreSQL Client Module Tests](postgresclient_module_tests.md): Tests for PostgreSQL connection and operations
- [Text Module Tests](text_module_tests.md): Tests for text manipulation, normalization, replacement, and template rendering
- [Buildah Module Tests](buildah_module_tests.md): Tests for container and image operations
- [Nerdctl Module Tests](nerdctl_module_tests.md): Tests for container and image operations using nerdctl
- [RFS Module Tests](rfs_module_tests.md): Tests for remote filesystem operations and filesystem layers
- [Running Tests](running_tests.md): Instructions for running all Rhai tests
- [CI Workflow](ci_workflow.md): Continuous integration workflow for Rhai tests

## Examples

For examples of how to use SAL's Rhai integration, see the `examples` directory in the project root. These examples demonstrate various features and use cases.

## Writing Your Own Scripts

When writing Rhai scripts that use SAL:

1. Import the necessary modules (they're automatically registered)
2. Use the functions provided by each module
3. Handle errors appropriately
4. Clean up resources when done

Example:

```rhai
// Simple example of using the OS module
let test_dir = "my_test_dir";
mkdir(test_dir);

if exist(test_dir) {
    print(`Directory ${test_dir} created successfully`);

    // Create a file
    let test_file = test_dir + "/test.txt";
    file_write(test_file, "Hello, world!");

    // Read the file
    let content = file_read(test_file);
    print(`File content: ${content}`);

    // Clean up
    delete(test_dir);
}
```

## API Reference

For detailed information about the functions available in each module, refer to the module-specific documentation.
