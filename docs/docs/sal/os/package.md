# os.package Module

### `package_install(package)`

Install a package using the system package manager.

- **Description**: Installs the specified `package` using the detected system package manager (e.g., `apt` on Ubuntu, `brew` on MacOS). Halts script execution if the package manager command fails. Returns a success message.
- **Returns**: `String` - A message indicating successful installation.
- **Arguments**:
    - `package`: `String` - The name of the package to install (e.g., `"nano"`).

```rhai
print("Installing 'nano' package...");
os::package_install("nano"); // Halts on package manager error
print("'nano' installed successfully.");
```

---

### `package_remove(package)`

Remove a package using the system package manager.

- **Description**: Removes the specified `package` using the detected system package manager. Halts script execution if the package manager command fails. Returns a success message.
- **Returns**: `String` - A message indicating successful removal.
- **Arguments**:
    - `package`: `String` - The name of the package to remove (e.g., `"htop"`).

```rhai
print("Removing 'htop' package...");
os::package_remove("htop"); // Halts on package manager error
print("'htop' removed successfully.");
```

---

### `package_update()`

Update package lists using the system package manager.

- **Description**: Updates the package lists that the system package manager uses (e.g., `apt update`, `brew update`). Halts script execution if the package manager command fails. Returns a success message.
- **Returns**: `String` - A message indicating successful update.
- **Arguments**: None.

```rhai
print("Updating package lists...");
os::package_update(); // Halts on package manager error
print("Package lists updated.");
```

---

### `package_upgrade()`

Upgrade installed packages using the system package manager.

- **Description**: Upgrades installed packages using the detected system package manager (e.g., `apt upgrade`, `brew upgrade`). Halts script execution if the package manager command fails. Returns a success message.
- **Returns**: `String` - A message indicating successful upgrade.
- **Arguments**: None.

```rhai
print("Upgrading installed packages...");
os::package_upgrade(); // Halts on package manager error
print("Packages upgraded.");
```

---

### `package_list()`

List installed packages using the system package manager.

- **Description**: Lists the names of packages installed on the system using the detected package manager. Halts script execution if the package manager command fails.
- **Returns**: `Array` of `String` - An array containing the names of installed packages.
- **Arguments**: None.

```rhai
print("Listing installed packages...");
let installed_packages = os::package_list(); // Halts on package manager error
for pkg in installed_packages {
    print(`- ${pkg}`);
}
```

---

### `package_search(query)`

Search for packages using the system package manager.

- **Description**: Searches for packages matching the given `query` using the detected system package manager. Halts script execution if the package manager command fails.
- **Returns**: `Array` of `String` - An array containing the search results (package names and/or descriptions).
- **Arguments**:
    - `query`: `String` - The search term.

```rhai
print("Searching for 'python' packages...");
let python_packages = os::package_search("python"); // Halts on package manager error
for pkg in python_packages {
    print(`- ${pkg}`);
}
```

---

### `package_is_installed(package)`

Check if a package is installed using the system package manager.

- **Description**: Checks if the specified `package` is installed using the detected system package manager. Halts script execution if the package manager command itself fails (e.g., command not found), but does NOT halt if the package is simply not found.
- **Returns**: `Boolean` - `true` if the package is installed, `false` otherwise.
- **Arguments**:
    - `package`: `String` - The name of the package to check (e.g., `"wget"`).

```rhai
let package_name = "wget";
if os::package_is_installed(package_name) { // Halts on package manager command error
    print(`${package_name} is installed.`);
} else {
    print(`${package_name} is not installed.`);
}
```

---

### `package_set_debug(debug)`

Set the debug mode for package management operations.

- **Description**: Enables or disables debug output for subsequent package management operations. This function does NOT halt on error and always returns the boolean value it was set to.
- **Returns**: `Boolean` - The boolean value that the debug flag was set to.
- **Arguments**:
    - `debug`: `Boolean` - Set to `true` to enable debug output, `false` to disable.

```rhai
print("Enabling package debug output.");
os::package_set_debug(true);
// Subsequent package operations will print debug info

print("Disabling package debug output.");
os::package_set_debug(false);
```

---

### `package_platform()`

Get the current platform name for package management.

- **Description**: Returns the name of the operating system platform as detected by the package manager logic. This function does NOT halt on error; it returns `"Unknown"` if the platform cannot be determined.
- **Returns**: `String` - The platform name, one of `"Ubuntu"`, `"MacOS"`, or `"Unknown"`.
- **Arguments**: None.

```rhai
let platform = os::package_platform(); // Does not halt on error
print(`Detected package platform: ${platform}`);
```
