# os.download Module

### `download(url, dest, min_size_kb)`

Download a file from URL to destination using the curl command.

- **Description**: Downloads a file from the given `url`. If `dest` is a directory, the filename is derived from the URL. If `dest` is a file path, it is used directly. Requires the `curl` command to be available. Halts script execution on download or file writing errors, or if the downloaded file size is less than `min_size_kb`. Returns the destination path.
- **Returns**: `String` - The path where the file was downloaded.
- **Arguments**:
    - `url`: `String` - The URL of the file to download.
    - `dest`: `String` - The destination path (directory or file).
    - `min_size_kb`: `Integer` - The minimum expected size of the downloaded file in kilobytes.

```rhai
let download_url = "https://example.com/archive.zip";
let download_dest_dir = "/tmp/downloads";
print(`Downloading ${download_url} to ${download_dest_dir}...`);
let downloaded_file_path = os::download(download_url, download_dest_dir, 50); // Halts on error
print(`Downloaded to: ${downloaded_file_path}`);
```

---

### `download_file(url, dest, min_size_kb)`

Download a file from URL to a specific file destination using the curl command.

- **Description**: Downloads a file from the given `url` directly to the specified file `dest`. Requires the `curl` command. Halts script execution on download or file writing errors, or if the downloaded file size is less than `min_size_kb`. Returns the destination path.
- **Returns**: `String` - The path where the file was downloaded.
- **Arguments**:
    - `url`: `String` - The URL of the file to download.
    - `dest`: `String` - The full path where the file should be saved.
    - `min_size_kb`: `Integer` - The minimum expected size of the downloaded file in kilobytes.

```rhai
let data_url = "https://example.com/dataset.tar.gz";
let local_path = "/opt/data/dataset.tar.gz";
print(`Downloading ${data_url} to ${local_path}...`);
os::download_file(data_url, local_path, 1024); // Halts on error
print(`Downloaded dataset to: ${local_path}`);
```

---

### `download_install(url, min_size_kb)`

Download a file and install it if it\'s a supported package format.

- **Description**: Downloads a file from the given `url` to a temporary location and then attempts to install it using the appropriate system package manager if the file format is supported (e.g., `.deb` on Ubuntu, `.pkg` or `.dmg` on MacOS). Requires the `curl` command and the system package manager. Halts script execution on download, installation, or file size errors. Returns a success message.
- **Returns**: `String` - A success message upon successful download and installation attempt.
- **Arguments**:
    - `url`: `String` - The URL of the package file to download and install.
    - `min_size_kb`: `Integer` - The minimum expected size of the downloaded file in kilobytes.

```rhai
let package_url = "https://example.com/mytool.deb";
print(`Downloading and installing ${package_url}...`);
os::download_install(package_url, 300); // Halts on error
print("Installation attempt finished.");
```
