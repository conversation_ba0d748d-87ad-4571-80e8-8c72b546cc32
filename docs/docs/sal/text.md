# Text Manipulation Tools

The SAL text module provides powerful text manipulation capabilities that can be used from Rhai scripts. These include text replacement (with regex support), template rendering, string normalization, and text formatting utilities.

## Table of Contents

- [Text Replacement](#text-replacement)
- [Template Rendering](#template-rendering)
- [String Normalization](#string-normalization)
- [Text Formatting](#text-formatting)

## Text Replacement

The text replacement tools allow you to perform simple or complex text replacements, with support for regular expressions, case-insensitive matching, and file operations.

### Basic Usage

```rhai
// Create a new text replacer
let replacer = text_replacer_new()
    .pattern("foo")         // Set the pattern to search for
    .replacement("bar")     // Set the replacement text
    .build();               // Build the replacer

// Apply the replacer to a string
let result = replacer.replace("foo bar foo");
// Result: "bar bar bar"
```

### Advanced Features

#### Regular Expressions

```rhai
// Create a replacer with regex support
let replacer = text_replacer_new()
    .pattern("\\bfoo\\b")   // Use regex pattern (word boundary)
    .replacement("bar")
    .regex(true)            // Enable regex mode
    .build();

// Apply the replacer to a string
let result = replacer.replace("foo foobar");
// Result: "bar foobar" (only replaces whole "foo" words)
```

#### Case-Insensitive Matching

```rhai
// Create a replacer with case-insensitive matching
let replacer = text_replacer_new()
    .pattern("foo")
    .replacement("bar")
    .regex(true)
    .case_insensitive(true) // Enable case-insensitive matching
    .build();

// Apply the replacer to a string
let result = replacer.replace("FOO foo Foo");
// Result: "bar bar bar"
```

#### Multiple Replacements

```rhai
// Chain multiple replacements
let replacer = text_replacer_new()
    .pattern("foo")
    .replacement("bar")
    .and()                  // Add another replacement operation
    .pattern("baz")
    .replacement("qux")
    .build();

// Apply the replacer to a string
let result = replacer.replace("foo baz");
// Result: "bar qux"
```

#### File Operations

```rhai
// Create a replacer
let replacer = text_replacer_new()
    .pattern("foo")
    .replacement("bar")
    .build();

// Replace in a file and get the result as a string
let result = replacer.replace_file("input.txt");

// Replace in a file and write back to the same file
replacer.replace_file_in_place("input.txt");

// Replace in a file and write to a new file
replacer.replace_file_to("input.txt", "output.txt");
```

## Template Rendering

The template rendering tools allow you to create and render templates with variables, using the powerful Tera template engine.

### Basic Usage

```rhai
// Create a template builder with a template file
let template = template_builder_open("template.txt")
    .add_var("name", "John")            // Add a string variable
    .add_var("age", 30)                 // Add a numeric variable
    .add_var("items", ["a", "b", "c"]); // Add an array variable

// Render the template
let result = template.render();

// Render to a file
template.render_to_file("output.txt");
```

### Template Variables

You can add variables of various types:

```rhai
let template = template_builder_open("template.txt")
    .add_var("name", "John")            // String
    .add_var("age", 30)                 // Integer
    .add_var("height", 1.85)            // Float
    .add_var("is_active", true)         // Boolean
    .add_var("items", ["a", "b", "c"]); // Array
```

### Using Map for Variables

```rhai
// Create a map of variables
let vars = #{
    name: "Alice",
    place: "Wonderland"
};

// Add all variables from the map
let template = template_builder_open("template.txt")
    .add_vars(vars);
```

### Template Syntax

The template engine uses Tera, which supports:

- Variable interpolation: `{{ variable }}`
- Conditionals: `{% if condition %}...{% endif %}`
- Loops: `{% for item in items %}...{% endfor %}`
- Filters: `{{ variable | filter }}`

Example template:

```
Hello, {{ name }}!

{% if show_greeting %}
Welcome to {{ place }}.
{% endif %}

Your items:
{% for item in items %}
  - {{ item }}{% if not loop.last %}{% endif %}
{% endfor %}
```

## String Normalization

The string normalization tools help convert strings to consistent formats for use as file names or paths.

### name_fix

Converts a string to a safe, normalized name by:
- Converting to lowercase
- Replacing spaces and special characters with underscores
- Removing non-alphanumeric characters

```rhai
let fixed_name = name_fix("Hello World!");
// Result: "hello_world"

let fixed_name = name_fix("File-Name.txt");
// Result: "file_name.txt"
```

### path_fix

Similar to name_fix, but preserves path separators:

```rhai
let fixed_path = path_fix("/path/to/Hello World!");
// Result: "/path/to/hello_world"

let fixed_path = path_fix("./relative/path/to/DOCUMENT-123.pdf");
// Result: "./relative/path/to/document_123.pdf"
```

## Text Formatting

Tools to help with text formatting and indentation.

### dedent

Removes common leading whitespace from multi-line strings:

```rhai
let indented_text = "    line 1
    line 2
        line 3";

let dedented = dedent(indented_text);
// Result: "line 1
// line 2
//     line 3"
```

### prefix

Adds a prefix to every line in a multi-line string:

```rhai
let text = "line 1
line 2
line 3";

let prefixed = prefix(text, "    ");
// Result: "    line 1
//     line 2
//     line 3"
```

## Examples

See the [text_tools.rhai](https://github.com/ourworld-tf/herocode/blob/main/sal/src/rhaiexamples/text_tools.rhai) example script for more detailed examples of using these text manipulation tools.