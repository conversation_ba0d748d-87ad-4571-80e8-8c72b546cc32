---
title: "os"
sidebar_position: 2
hide_title: true
---

# OS Tools

The OS module provides functions for working with files, directories, and downloading files from the internet.

## File System Functions

### `copy(src, dest)`

Recursively copies a file or directory from source to destination.

**Parameters:**
- `src` (string): The source file or directory path
- `dest` (string): The destination path

**Returns:** A message confirming the copy was successful.

**Example:**
```js
// Copy a file
copy("source.txt", "destination.txt");

// Copy a directory recursively
copy("source_dir", "destination_dir");
```

### `exist(path)`

Checks if a file or directory exists.

**Parameters:**
- `path` (string): The path to check

**Returns:** A boolean value - `true` if the file or directory exists, `false` otherwise.

**Example:**
```js
if exist("config.json") {
    // File exists, do something
} else {
    // File doesn't exist
}
```

### `find_file(dir, filename)`

Finds a file in a directory with support for wildcards.

**Parameters:**
- `dir` (string): The directory to search in
- `filename` (string): The filename pattern to search for (supports wildcards)

**Returns:** The path of the first matching file.

**Example:**
```js
// Find a specific file
let config_file = find_file("./config", "settings.json");

// Find using wildcards
let log_file = find_file("./logs", "*.log");
```

### `find_files(dir, filename)`

Finds multiple files in a directory recursively with support for wildcards.

**Parameters:**
- `dir` (string): The directory to search in
- `filename` (string): The filename pattern to search for (supports wildcards)

**Returns:** A list of matching file paths.

**Example:**
```js
// Find all JSON files
let json_files = find_files("./data", "*.json");

// Process each file
for file in json_files {
    print(`Found file: ${file}`);
}
```

### `find_dir(dir, dirname)`

Finds a directory in a parent directory with support for wildcards.

**Parameters:**
- `dir` (string): The parent directory to search in
- `dirname` (string): The directory name pattern to search for (supports wildcards)

**Returns:** The path of the first matching directory.

**Example:**
```js
// Find a specific directory
let config_dir = find_dir("./", "config");

// Find using wildcards
let version_dir = find_dir("./releases", "v*");
```

### `find_dirs(dir, dirname)`

Finds multiple directories in a parent directory recursively with support for wildcards.

**Parameters:**
- `dir` (string): The parent directory to search in
- `dirname` (string): The directory name pattern to search for (supports wildcards)

**Returns:** A list of matching directory paths.

**Example:**
```js
// Find all version directories
let version_dirs = find_dirs("./releases", "v*");

// Process each directory
for dir in version_dirs {
    print(`Found directory: ${dir}`);
}
```

### `delete(path)`

Deletes a file or directory. This function is defensive and doesn't error if the file doesn't exist.

**Parameters:**
- `path` (string): The path of the file or directory to delete

**Returns:** A message confirming the deletion was successful.

**Example:**
```js
// Delete a file
delete("temp.txt");

// Delete a directory
delete("temp_dir");
```

### `mv(src, dest)`

Moves a file or directory from source to destination.

**Parameters:**
- `src` (string): The source path
- `dest` (string): The destination path

**Returns:** A message confirming the move was successful.

**Example:**
```js
// Move a file
mv("file.txt", "new_location/file.txt");

// Move a directory
mv("source_dir", "destination_dir");

// Rename a file
mv("old_name.txt", "new_name.txt");
```

### `mkdir(path)`

Creates a directory and all parent directories. This function is defensive and doesn't error if the directory already exists.

**Parameters:**
- `path` (string): The path of the directory to create

**Returns:** A message confirming the directory was created.

**Example:**
```js
// Create a directory
mkdir("new_dir");

// Create nested directories
mkdir("parent/child/grandchild");
```

### `file_size(path)`

Gets the size of a file in bytes.

**Parameters:**
- `path` (string): The path of the file

**Returns:** The size of the file in bytes.

**Example:**
```js
// Get file size
let size = file_size("large_file.dat");
print(`File size: ${size} bytes`);
```

## File Content Functions

### `file_read(path)`

Reads the contents of a file.

**Parameters:**
- `path` (string): The path of the file to read

**Returns:** The content of the file as a string.

**Example:**
```js
// Read a file
let content = file_read("config.json");
print(`File content: ${content}`);
```

### `file_write(path, content)`

Writes content to a file. Creates the file if it doesn't exist, overwrites if it does.

**Parameters:**
- `path` (string): The path of the file to write to
- `content` (string): The content to write to the file

**Returns:** A message confirming the file was written.

**Example:**
```js
// Write to a file
file_write("config.json", "{\n  \"setting\": \"value\"\n}");
```

### `file_write_append(path, content)`

Appends content to a file. Creates the file if it doesn't exist.

**Parameters:**
- `path` (string): The path of the file to append to
- `content` (string): The content to append to the file

**Returns:** A message confirming the content was appended.

**Example:**
```js
// Append to a log file
file_write_append("log.txt", "New log entry\n");
```

### `rsync(src, dest)`

Syncs directories using rsync (or platform equivalent).

**Parameters:**
- `src` (string): The source directory
- `dest` (string): The destination directory

**Returns:** A message confirming the directories were synced.

**Example:**
```js
// Sync directories
rsync("source_dir", "backup_dir");
```

### `chdir(path)`

Changes the current working directory.

**Parameters:**
- `path` (string): The path to change to

**Returns:** A message confirming the directory was changed.

**Example:**
```js
// Change directory
chdir("project/src");
```

## Download Functions

### `download(url, dest, min_size_kb)`

Downloads a file from a URL to a destination directory using the curl command. If the URL ends with a supported archive format, the file will be automatically extracted to the destination directory.

**Supported archive formats for automatic extraction:**
- `.tar.gz`
- `.tgz`
- `.tar`
- `.zip`

**Parameters:**
- `url` (string): The URL to download from
- `dest` (string): The destination directory where the file will be saved or extracted
- `min_size_kb` (integer): The minimum expected file size in kilobytes (for validation)

**Returns:** The path where the file was saved or extracted.

**Example:**
```js
// Download a file to a directory
download("https://example.com/file.zip", "downloads/", 10);
```

### `download_file(url, dest, min_size_kb)`

Downloads a file from a URL to a specific file destination using the curl command. This function is designed for downloading files to a specific path, not for extracting archives.

**Parameters:**
- `url` (string): The URL to download from
- `dest` (string): The destination file path where the file will be saved
- `min_size_kb` (integer): The minimum expected file size in kilobytes (for validation)

**Returns:** The path where the file was saved.

**Example:**
```js
// Download a file to a specific path
download_file("https://example.com/file.txt", "downloads/myfile.txt", 10);
```

### `download_install(url, min_size_kb)`

Downloads a file and installs it if it's a supported package format.

**Supported package formats for automatic installation:**
- `.deb` packages on Debian-based systems

**Parameters:**
- `url` (string): The URL to download from
- `min_size_kb` (integer): The minimum expected file size in kilobytes (for validation)

**Returns:** The path where the file was saved or installed.

**Example:**
```js
// Download and install a package
download_install("https://example.com/package.deb", 1000);
```

### `chmod_exec(path)`

Makes a file executable (equivalent to `chmod +x` in Unix).

**Parameters:**
- `path` (string): The path to the file to make executable

**Returns:** A message confirming the file was made executable.

**Example:**
```js
// Make a file executable
chmod_exec("downloads/script.sh");
```