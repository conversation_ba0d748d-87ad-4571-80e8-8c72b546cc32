---
title: "process"
sidebar_position: 3
hide_title: true
---

# Process Module

The Process module provides functions for running commands and managing processes on your system.

## Command Results

### Command Output Information

When you run a command, you get back information about what happened:

- `stdout`: The normal output of the command
- `stderr`: Any error messages from the command
- `success`: Whether the command worked (true) or failed (false)
- `code`: The exit code (0 usually means success)

### Process Information

When you get information about a running process, you can see:

- `pid`: The process ID number
- `name`: The name of the process
- `memory`: How much memory the process is using
- `cpu`: How much CPU the process is using

## Run Functions
### `run(command)`

Runs a command or multiline script with arguments.

**Parameters:**
- `command` (string): The command to run (can be a single command or a multiline script)

**Returns:** The result of the command, including output and whether it succeeded.

**Example 1: Running a simple command**
```js
// Run a simple command
let result = run("ls -la");

// Check if the command was successful
if result.success {
    print(`Command output: ${result.stdout}`);
} else {
    print(`Command failed with error: ${result.stderr}`);
}
```

**Example 2: Running a multiline script**
```js
// Create a multiline script using backtick string literals
let setup_script = `
# Create directories
mkdir -p /tmp/test_project
cd /tmp/test_project

# Initialize git repository
git init
echo 'Initial content' > README.md
git add README.md
git config --local user.email '<EMAIL>'
git config --local user.name 'Test User'
git commit -m 'Initial commit'
`;

// Execute the multiline script
let result = run(setup_script);

```



### `run_silent(command)`

Runs a command or multiline script with arguments silently (without displaying output).

**Parameters:**
- `command` (string): The command to run

**Returns:** The result of the command, without displaying the output.

**Example:**

```js
// Run a command silently
let result = run_silent("git pull");

// Check the exit code
if result.code == 0 {
    print("Git pull successful");
} else {
    print(`Git pull failed with code ${result.code}`);
}
```

### `new_run_options()`

Creates a new map with default run options.

**Returns:** A map with the following default options:
- `die` (boolean): `true` - Whether to throw an error if the command fails
- `silent` (boolean): `false` - Whether to suppress command output
- `async_exec` (boolean): `false` - Whether to run the command asynchronously
- `log` (boolean): `false` - Whether to log the command execution

**Example:**
```js
// Create run options
let options = new_run_options();
```

### `run_with_options(command, options)`

Runs a command with options specified in a map.

**Parameters:**
- `command` (string): The command to run
- `options` (map): A map of options created with `new_run_options()`

**Returns:** The result of the command with your custom settings applied.

**Example:**
```js
// Create and customize run options
let options = new_run_options();
options.die = false;      // Don't throw an error if the command fails
options.silent = true;    // Suppress command output
options.async_exec = false; // Run synchronously
options.log = true;       // Log the command execution

// Run a command with options
let result = run_with_options("npm install", options);
```

## Working with Multiline Scripts

The Process module allows you to execute multiline scripts, which is particularly useful for complex operations that require multiple commands to be executed in sequence.

### Creating Multiline Scripts

Multiline scripts can be created using backtick (`) string literals in HeroScript:

```js
let my_script = `
# This is a multiline bash script
echo "Hello, World!"
mkdir -p /tmp/my_project
cd /tmp/my_project
touch example.txt
`;
```

## Process Management Functions

### `which(cmd)`

Checks if a command exists in the PATH.

**Parameters:**
- `cmd` (string): The command to check

**Returns:** The full path to the command if found, or nothing if not found.

**Example:**
```js
// Check if a command exists
let git_path = which("git");

if git_path != () {
    print(`Git is installed at: ${git_path}`);
} else {
    print("Git is not installed");
}
```

### `kill(pattern)`

Kills processes matching a pattern.

**Parameters:**
- `pattern` (string): The pattern to match process names against

**Returns:** A message confirming the processes were killed.

**Example:**
```js
// Kill all processes with "node" in their name
kill("node");
```

### `process_list(pattern)`

Lists processes matching a pattern (or all processes if the pattern is empty).

**Parameters:**
- `pattern` (string): The pattern to match process names against (can be empty to list all processes)

**Returns:** A list of processes matching your search.

**Example:**
```js
// List all processes
let all_processes = process_list("");

// List processes containing "node" in their name
let node_processes = process_list("node");

// Display process information
for process in node_processes {
    print(`PID: ${process.pid}, Name: ${process.name}, Memory: ${process.memory}, CPU: ${process.cpu}`);
}
```

### `process_get(pattern)`

Gets a single process matching the pattern. Throws an error if zero or more than one process matches.

**Parameters:**
- `pattern` (string): The pattern to match process names against

**Returns:** Information about the matching process. This will only work if exactly one process matches.

**Example:**
```js
// Try to get a specific process
try {
    let process = process_get("my_app");
    print(`Found process: PID=${process.pid}, Name=${process.name}`);
} catch(err) {
    print(`Error: ${err}`);
}
```