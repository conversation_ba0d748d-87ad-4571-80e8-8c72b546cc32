---
title: "build containers"
sidebar_position: 20
hide_title: true
---

# Container Builder

The Buildah module provides functions for working with containers and images using the Buildah tool. Buildah helps you create and manage container images.

## Builder Pattern

The Buildah module now supports a Builder pattern, which provides a more intuitive and flexible way to work with containers and images.

### Creating a Builder

```js
// Create a builder with a name and base image
let builder = bah_new("my-container", "alpine:latest");

// Access builder properties
let container_id = builder.container_id;
let name = builder.name;
let image = builder.image;
```

### Builder Methods
The Builder object provides the following methods:

- `run(command)`: Run a command in the container
- `run_with_isolation(command, isolation)`: Run a command with specified isolation
- `copy(source, dest)`: Copy files into the container
- `add(source, dest)`: Add files into the container
- `commit(image_name)`: Commit the container to an image
- `remove()`: Remove the container
- `reset()`: Remove the container and clear the container_id
- `config(options)`: Configure container metadata
- `set_entrypoint(entrypoint)`: Set the entrypoint for the container
- `set_cmd(cmd)`: Set the default command for the container
- `debug_mode`: Get or set the debug flag (true/false)
- `write_content(content, dest_path)`: Write content to a file in the container
- `read_content(source_path)`: Read content from a file in the container
- `images()`: List images in local storage
- `image_remove(image)`: Remove an image
- `image_pull(image, tls_verify)`: Pull an image from a registry
- `image_push(image, destination, tls_verify)`: Push an image to a registry
- `image_tag(image, new_name)`: Add a tag to an image
- `build(tag, context_dir, file, isolation)`: Build an image from a Dockerfile
- `build(tag, context_dir, file, isolation)`: Build an image from a Dockerfile

### Example

```js
// Create a builder
let builder = bah_new("my-container", "alpine:latest");

// Enable debug mode to see command output
builder.debug_mode = true;

// Reset the builder to remove any existing container
builder.reset();

// Create a new container
builder = bah_new("my-container", "alpine:latest");

// Run a command
let result = builder.run("echo 'Hello from container'");
println(`Command output: ${result.stdout}`);

// Write content directly to a file in the container
let script_content = `#!/bin/sh
echo "Hello from startup script"
`;
builder.write_content(script_content, "/start.sh");
builder.run("chmod +x /start.sh");

// Set the entrypoint for the container
builder.set_entrypoint("/start.sh");

// Add a file
file_write("test_file.txt", "Test content");
builder.add("test_file.txt", "/");

// Commit to an image
builder.commit("my-custom-image:latest");

// Clean up
builder.remove();
delete("test_file.txt");
```

## Image Information

### Image Properties

When working with images, you can access the following information:

- `id`: The unique identifier for the image
- `names`: A list of names/tags for the image
- `name`: The primary name of the image, or `<none>` if the image has no names
- `size`: The size of the image
- `created`: When the image was created

## Builder Methods

### `bah_new(name, image)`

Creates a new Builder object for working with a container.

**Parameters:**
- `name` (string): The name to give the container
- `image` (string): The name or ID of the image to create the container from

**Returns:** A Builder object if successful.

**Example:**
```js
// Create a new Builder
let builder = bah_new("my-container", "alpine:latest");
```

**Notes:**
- If a container with the given name already exists, it will be reused instead of creating a new one
- The Builder object provides methods for working with the container

### `reset()`

Resets a Builder by removing the container and clearing the container_id. This allows you to start fresh with the same Builder object.

**Returns:** Nothing.

**Example:**
```js
// Create a Builder
let builder = bah_new("my-container", "alpine:latest");

// Reset the Builder to remove the container
builder.reset();

// Create a new container with the same name
builder = bah_new("my-container", "alpine:latest");
```

### `debug_mode`

Get or set the debug flag for the Builder. When debug mode is enabled, all buildah commands will output their stdout/stderr, making it easier to debug issues.

**Example:**
```js
// Create a Builder
let builder = bah_new("my-container", "alpine:latest");

// Enable debug mode
builder.debug_mode = true;

// Run a command with debug output
builder.run("echo 'Hello with debug'");

// Disable debug mode
builder.debug_mode = false;
```

### `set_entrypoint(entrypoint)`

Sets the entrypoint for the container. The entrypoint is the command that will be executed when the container starts.

**Parameters:**
- `entrypoint` (string): The entrypoint command

**Returns:** Command result if successful.

**Example:**
```js
// Create a Builder
let builder = bah_new("my-container", "alpine:latest");

// Set the entrypoint
builder.set_entrypoint("/start.sh");
```

### `set_cmd(cmd)`

Sets the default command for the container. This is used as arguments to the entrypoint.

**Parameters:**
- `cmd` (string): The default command

**Returns:** Command result if successful.

**Example:**
```js
// Create a Builder
let builder = bah_new("my-container", "alpine:latest");

// Set the default command
builder.set_cmd("--verbose");
```

### `write_content(content, dest_path)`

Writes content to a file in the container.

**Parameters:**
- `content` (string): The content to write
- `dest_path` (string): The destination path in the container

**Returns:** Command result if successful.

**Example:**
```js
// Create a Builder
let builder = bah_new("my-container", "alpine:latest");

// Write content to a file
let content = "Hello, world!";
builder.write_content(content, "/hello.txt");
```

### `read_content(source_path)`

Reads content from a file in the container.

**Parameters:**
- `source_path` (string): The source path in the container

**Returns:** The file content as a string if successful.

**Example:**
```js
// Create a Builder
let builder = bah_new("my-container", "alpine:latest");

// Write content to a file
builder.write_content("Hello, world!", "/hello.txt");

// Read content from the file
let content = builder.read_content("/hello.txt");
println(content);  // Outputs: Hello, world!
```
