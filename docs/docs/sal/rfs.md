# RFS (Remote File System)

The RFS module provides a Rust wrapper for the RFS tool, which allows mounting remote filesystems locally and managing filesystem layers.

## Overview

RFS (Remote File System) is a tool that enables mounting various types of remote filesystems locally, as well as creating and managing filesystem layers. The SAL library provides a Rust wrapper for RFS with a fluent builder API, making it easy to use in your applications.

## Features

- Mount remote filesystems locally (SSH, S3, WebDAV, etc.)
- List mounted filesystems
- Unmount filesystems
- Pack directories into filesystem layers
- Unpack filesystem layers
- List contents of filesystem layers
- Verify filesystem layers

## Usage in Rust

### Mounting a Filesystem

```rust
use sal::virt::rfs::{RfsBuilder, MountType};

// Create a new RFS builder
let mount = RfsBuilder::new("<EMAIL>:/remote/path", "/local/mount/point", MountType::SSH)
    .with_option("port", "2222")
    .with_option("identity_file", "/path/to/key")
    .with_debug(true)
    .mount()?;

println!("Mounted filesystem with ID: {}", mount.id);
```

### Listing Mounts

```rust
use sal::virt::rfs::list_mounts;

// List all mounts
let mounts = list_mounts()?;
for mount in mounts {
    println!("Mount ID: {}, Source: {}, Target: {}", mount.id, mount.source, mount.target);
}
```

### Unmounting a Filesystem

```rust
use sal::virt::rfs::unmount;

// Unmount a filesystem
unmount("/local/mount/point")?;
```

### Packing a Directory

```rust
use sal::virt::rfs::{PackBuilder, StoreSpec};

// Create store specifications
let store_spec = StoreSpec::new("file")
    .with_option("path", "/path/to/store");

// Pack a directory with builder pattern
let result = PackBuilder::new("/path/to/directory", "output.fl")
    .with_store_spec(store_spec)
    .with_debug(true)
    .pack()?;
```

### Unpacking a Filesystem Layer

```rust
use sal::virt::rfs::unpack;

// Unpack a filesystem layer
unpack("input.fl", "/path/to/unpack")?;
```

## Usage in Rhai Scripts

### Mounting a Filesystem

```rhai
// Create a map for mount options
let options = #{
    "port": "22",
    "identity_file": "/path/to/key",
    "readonly": "true"
};

// Mount the directory
let mount = rfs_mount("<EMAIL>:/remote/path", "/local/mount/point", "ssh", options);

print(`Mounted ${mount.source} to ${mount.target} with ID: ${mount.id}`);
```

### Listing Mounts

```rhai
// List all mounts
let mounts = rfs_list_mounts();
print(`Number of mounts: ${mounts.len()}`);

for mount in mounts {
    print(`Mount ID: ${mount.id}, Source: ${mount.source}, Target: ${mount.target}`);
}
```

### Unmounting a Filesystem

```rhai
// Unmount the directory
rfs_unmount("/local/mount/point");
```

### Packing a Directory

```rhai
// Pack the directory
// Store specs format: "file:path=/path/to/store,s3:bucket=my-bucket"
rfs_pack("/path/to/directory", "output.fl", "file:path=/path/to/store");
```

### Unpacking a Filesystem Layer

```rhai
// Unpack the filesystem layer
rfs_unpack("output.fl", "/path/to/unpack");
```

## Mount Types

The RFS module supports various mount types:

- **Local**: Mount a local directory
- **SSH**: Mount a remote directory via SSH
- **S3**: Mount an S3 bucket
- **WebDAV**: Mount a WebDAV server

## Store Specifications

When packing a directory into a filesystem layer, you can specify one or more stores to use. Each store has a type and options:

- **File**: Store files on the local filesystem
  - Options: `path` (path to the store)
- **S3**: Store files in an S3 bucket
  - Options: `bucket` (bucket name), `region` (AWS region), `access_key`, `secret_key`

## Examples

See the [RFS example script](../../rhaiexamples/rfs_example.rhai) for more examples of how to use the RFS module in Rhai scripts.