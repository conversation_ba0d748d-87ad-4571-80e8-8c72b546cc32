name: Rhai Tests

on:
  push:
    branches: [ '*' ]
    paths:
      - 'src/rhai_tests/**'
      - 'src/rhai/**'
      - 'src/git/**'
      - 'src/os/**'
      - 'run_rhai_tests.sh'
      - '.github/workflows/rhai-tests.yml'
  pull_request:
    branches: [ '*' ]
    paths:
      - 'src/rhai_tests/**'
      - 'src/rhai/**'
      - 'src/git/**'
      - 'src/os/**'
      - 'run_rhai_tests.sh'
      - '.github/workflows/rhai-tests.yml'
  workflow_dispatch:  # Allow manual triggering

jobs:
  rhai-tests:
    name: Run Rhai Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
      
      - name: Cache Rust dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-
      
      - name: Build herodo
        run: |
          cargo build --bin herodo
          echo "${{ github.workspace }}/target/debug" >> $GITHUB_PATH
      
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y git curl
      
      - name: Run Rhai tests
        run: |
          chmod +x run_rhai_tests.sh
          ./run_rhai_tests.sh
      
      - name: Check for test failures
        run: |
          if grep -q "Some tests failed" run_rhai_tests.log; then
            echo "::error::Some Rhai tests failed. Check the logs for details."
            exit 1
          else
            echo "All Rhai tests passed!"
          fi
        if: always()
