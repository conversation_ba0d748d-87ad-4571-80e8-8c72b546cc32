# Hero Vault Symmetric Encryption Module

The Symmetric Encryption module provides functionality for symmetric encryption and decryption using the ChaCha20Poly1305 algorithm.

## Module Structure

The Symmetric Encryption module is organized into:

- `implementation.rs` - Core implementation of symmetric encryption functionality
- `mod.rs` - Module exports and public interface

## Key Features

### Key Generation

The module provides functionality for generating secure symmetric keys:

```rust
// Generate a new symmetric key
let key = generate_key()?;
```

### Encryption

The module provides functionality for encrypting data using ChaCha20Poly1305:

```rust
// Encrypt data
let encrypted = encrypt(&key, "This is a secret message")?;
```

### Decryption

The module provides functionality for decrypting data encrypted with ChaCha20Poly1305:

```rust
// Decrypt data
let decrypted = decrypt(&key, &encrypted)?;
```

### Password-Based Key Derivation

The module provides functionality for deriving encryption keys from passwords:

```rust
// Derive a key from a password
let key = derive_key_from_password(password, salt)?;
```

## Technical Details

### ChaCha20Poly1305

The module uses the ChaCha20Poly1305 authenticated encryption with associated data (AEAD) algorithm, which provides both confidentiality and integrity protection.

ChaCha20 is a stream cipher designed by Daniel J. Bernstein, which is combined with the Poly1305 message authentication code to provide authenticated encryption.

Key features of ChaCha20Poly1305:

- 256-bit key
- 96-bit nonce (used once)
- Authentication tag to verify integrity
- High performance on modern processors
- Resistance to timing attacks

### Key Derivation

For password-based encryption, the module uses the PBKDF2 (Password-Based Key Derivation Function 2) algorithm to derive encryption keys from passwords.

Key features of PBKDF2:

- Configurable iteration count to increase computational cost
- Salt to prevent rainbow table attacks
- Configurable output key length
- Uses HMAC-SHA256 as the underlying pseudorandom function

## Security Considerations

- Always use a unique key for each encryption operation
- Never reuse nonces with the same key
- Store keys securely
- Use strong passwords for password-based encryption
- Consider the security implications of storing encrypted data

## Error Handling

The module uses the `CryptoError` type for handling errors that can occur during symmetric encryption operations:

- `InvalidKeyLength` - Invalid key length
- `EncryptionFailed` - Encryption failed
- `DecryptionFailed` - Decryption failed

## Examples

For examples of how to use the Symmetric Encryption module, see the `examples/hero_vault` directory, particularly:

- `example.rhai` - Basic example demonstrating symmetric encryption
- `advanced_example.rhai` - Advanced example with error handling
