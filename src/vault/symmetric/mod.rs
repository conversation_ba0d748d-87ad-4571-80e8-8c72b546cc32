//! Symmetric encryption functionality
//!
//! This module provides functionality for symmetric encryption using ChaCha20Poly1305.

pub mod implementation;

// Re-export public types and functions
pub use implementation::{
    generate_symmetric_key, derive_key_from_password,
    encrypt_symmetric, decrypt_symmetric,
    encrypt_with_key, decrypt_with_key,
    encrypt_key_space, decrypt_key_space,
    serialize_encrypted_space, deserialize_encrypted_space,
    EncryptedKeySpace, EncryptedKeySpaceMetadata
};
