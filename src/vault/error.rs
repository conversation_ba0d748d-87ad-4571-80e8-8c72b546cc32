//! Error types for cryptographic operations

use thiserror::Error;

/// Errors that can occur during cryptographic operations
#[derive(Erro<PERSON>, Debug)]
pub enum CryptoError {
    /// Invalid key length
    #[error("Invalid key length")]
    InvalidKeyLength,

    /// Encryption failed
    #[error("Encryption failed: {0}")]
    EncryptionFailed(String),

    /// Decryption failed
    #[error("Decryption failed: {0}")]
    DecryptionFailed(String),

    /// Signature format error
    #[error("Signature format error: {0}")]
    SignatureFormatError(String),

    /// Keypair already exists
    #[error("Keypair already exists: {0}")]
    KeypairAlreadyExists(String),

    /// Keypair not found
    #[error("Keypair not found: {0}")]
    KeypairNotFound(String),

    /// No active key space
    #[error("No active key space")]
    NoActiveSpace,

    /// No keypair selected
    #[error("No keypair selected")]
    NoKeypairSelected,

    /// Serialization error
    #[error("Serialization error: {0}")]
    SerializationError(String),

    /// Invalid address format
    #[error("Invalid address format: {0}")]
    InvalidAddress(String),

    /// Smart contract error
    #[error("Smart contract error: {0}")]
    ContractError(String),
}

/// Convert CryptoError to SAL's Error type
impl From<CryptoError> for crate::Error {
    fn from(err: CryptoError) -> Self {
        crate::Error::Sal(err.to_string())
    }
}
