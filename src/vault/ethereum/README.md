# Hero Vault Ethereum Module

The Ethereum module provides functionality for creating and managing Ethereum wallets and interacting with smart contracts on EVM-based blockchains.

## Module Structure

The Ethereum module is organized into several components:

- `wallet.rs` - Core Ethereum wallet implementation
- `networks.rs` - Network registry and configuration
- `provider.rs` - Provider creation and management
- `transaction.rs` - Transaction-related functionality
- `storage.rs` - Wallet storage functionality
- `contract.rs` - Smart contract interaction functionality
- `contract_utils.rs` - Utilities for contract interactions

## Key Features

### Wallet Management

The module provides functionality for creating and managing Ethereum wallets:

```rust
// Create a new Ethereum wallet for a specific network
let wallet = create_ethereum_wallet_for_network("Ethereum")?;

// Create a wallet for specific networks
let peaq_wallet = create_peaq_wallet()?;
let agung_wallet = create_agung_wallet()?;

// Create a wallet with a specific name
let named_wallet = create_ethereum_wallet_from_name_for_network("my_wallet", "Gnosis")?;

// Create a wallet from a private key
let imported_wallet = create_ethereum_wallet_from_private_key("0x...")?;

// Get the current wallet for a network
let current_wallet = get_current_ethereum_wallet_for_network("Ethereum")?;

// Clear wallets
clear_ethereum_wallets()?;
clear_ethereum_wallets_for_network("Gnosis")?;
```

### Network Management

The module supports multiple Ethereum networks and provides functionality for managing network configurations:

```rust
// Get a network configuration by name
let network = get_network_by_name("Ethereum")?;

// Get the proper network name (normalized)
let name = get_proper_network_name("eth")?; // Returns "Ethereum"

// List all available network names
let networks = list_network_names()?;

// Get all network configurations
let all_networks = get_all_networks()?;
```

### Provider Management

The module provides functionality for creating and managing Ethereum providers:

```rust
// Create a provider for a specific network
let provider = create_provider("Ethereum")?;

// Create providers for specific networks
let gnosis_provider = create_gnosis_provider()?;
let peaq_provider = create_peaq_provider()?;
let agung_provider = create_agung_provider()?;
```

### Transaction Management

The module provides functionality for managing Ethereum transactions:

```rust
// Get the balance of an address
let balance = get_balance("Ethereum", "0x...")?;

// Send ETH to an address
let tx_hash = send_eth("Ethereum", "0x...", "1000000000000000")?;

// Format a balance for display
let formatted = format_balance(balance, 18)?; // Convert wei to ETH
```

### Smart Contract Interactions

The module provides functionality for interacting with smart contracts:

```rust
// Load a contract ABI from JSON
let abi = load_abi_from_json(json_string)?;

// Create a contract instance
let contract = Contract::new(provider, "0x...", abi)?;

// Call a read-only function
let result = call_read_function(contract, "balanceOf", vec!["0x..."])?;

// Call a write function
let tx_hash = call_write_function(contract, "transfer", vec!["0x...", "1000"])?;

// Estimate gas for a function call
let gas = estimate_gas(contract, "transfer", vec!["0x...", "1000"])?;
```

### Contract Utilities

The module provides utilities for working with contract function arguments and return values:

```rust
// Convert Rhai values to Ethereum tokens
let token = convert_rhai_to_token(value)?;

// Prepare function arguments
let args = prepare_function_arguments(function, vec![arg1, arg2])?;

// Convert Ethereum tokens to Rhai values
let rhai_value = convert_token_to_rhai(token)?;

// Convert a token to a dynamic value
let dynamic = token_to_dynamic(token)?;
```

## Supported Networks

The module supports multiple Ethereum networks, including:

- Gnosis Chain
- Peaq Network
- Agung Network

Each network has its own configuration, including:

- RPC URL
- Chain ID
- Explorer URL
- Native currency symbol and decimals

## Error Handling

The module uses the `CryptoError` type for handling errors that can occur during Ethereum operations:

- `InvalidAddress` - Invalid Ethereum address format
- `ContractError` - Smart contract interaction error

## Examples

For examples of how to use the Ethereum module, see the `examples/hero_vault` directory, particularly:

- `contract_example.rhai` - Demonstrates loading a contract ABI and interacting with smart contracts
- `agung_simple_transfer.rhai` - Shows how to perform a simple ETH transfer on the Agung network
- `agung_send_transaction.rhai` - Demonstrates sending transactions on the Agung network
- `agung_contract_with_args.rhai` - Shows how to interact with contracts with arguments on Agung
