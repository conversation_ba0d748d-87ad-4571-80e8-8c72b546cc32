//! Ethereum provider functionality.

use ethers::prelude::*;

use crate::vault::error::CryptoError;
use super::networks::{self, NetworkConfig};

/// Creates a provider for a specific network.
pub fn create_provider(network: &NetworkConfig) -> Result<Provider<Http>, CryptoError> {
    Provider::<Http>::try_from(network.rpc_url.as_str())
        .map_err(|e| CryptoError::SerializationError(format!("Failed to create provider for {}: {}", network.name, e)))
}

/// Creates a provider for the Gnosis Chain.
pub fn create_gnosis_provider() -> Result<Provider<Http>, CryptoError> {
    create_provider(&networks::gnosis())
}

/// Creates a provider for the Peaq network.
pub fn create_peaq_provider() -> Result<Provider<Http>, CryptoError> {
    create_provider(&networks::peaq())
}

/// Creates a provider for the Agung testnet.
pub fn create_agung_provider() -> Result<Provider<Http>, CryptoError> {
    create_provider(&networks::agung())
}
