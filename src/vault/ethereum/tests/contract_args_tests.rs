//! Tests for smart contract argument handling functionality.

use ethers::types::Address;
use std::str::FromStr;

use crate::vault::ethereum::*;

#[test]
fn test_contract_creation() {
    // Create a simple ABI
    let abi_json = r#"[
        {
            "inputs": [],
            "name": "getValue",
            "outputs": [{"type": "uint256", "name": ""}],
            "stateMutability": "view",
            "type": "function"
        },
        {
            "inputs": [{"type": "uint256", "name": "newValue"}],
            "name": "setValue",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }
    ]"#;

    // Parse the ABI
    let abi = load_abi_from_json(abi_json).unwrap();

    // Create a contract address
    let address = Address::from_str("******************************************").unwrap();

    // Create a network config
    let network = networks::gnosis();

    // Create a contract
    let contract = Contract::new(address, abi, network);

    // Verify the contract was created correctly
    assert_eq!(contract.address, address);
    assert_eq!(contract.network.name, "Gnosis");

    // Verify the ABI contains the expected functions
    assert!(contract.abi.function("getValue").is_ok());
    assert!(contract.abi.function("setValue").is_ok());
}
