//! Tests for Ethereum transaction functionality.

use crate::vault::ethereum::*;
use crate::vault::keypair::implementation::KeyPair;
use ethers::types::U256;
// use std::str::FromStr;

#[test]
fn test_format_balance() {
    let network = networks::gnosis();
    
    // Test with 0
    let balance = U256::from(0);
    let formatted = format_balance(balance, &network);
    assert_eq!(formatted, "0.000000 xDAI");
    
    // Test with 1 wei
    let balance = U256::from(1);
    let formatted = format_balance(balance, &network);
    assert_eq!(formatted, "0.000000 xDAI");
    
    // Test with 1 gwei (10^9 wei)
    let balance = U256::from(1_000_000_000u64);
    let formatted = format_balance(balance, &network);
    assert_eq!(formatted, "0.000000 xDAI");
    
    // Test with 1 ETH (10^18 wei)
    let balance = U256::from_dec_str("1000000000000000000").unwrap();
    let formatted = format_balance(balance, &network);
    assert_eq!(formatted, "1.000000 xDAI");
    
    // Test with a larger amount
    let balance = U256::from_dec_str("123456789000000000000").unwrap();
    let formatted = format_balance(balance, &network);
    assert_eq!(formatted, "123.456789 xDAI");
}

#[test]
fn test_get_balance() {
    // This is a mock test since we can't actually query the blockchain in a unit test
    // In a real test, we would use a local blockchain or mock the provider
    
    // Create a provider
    let network = networks::gnosis();
    let provider_result = create_provider(&network);
    
    // The provider creation should succeed
    assert!(provider_result.is_ok());
    
    // We can't actually test get_balance without a blockchain
    // In a real test, we would mock the provider and test the function
}

#[test]
fn test_send_eth() {
    // This is a mock test since we can't actually send transactions in a unit test
    // In a real test, we would use a local blockchain or mock the provider
    
    // Create a wallet
    let keypair = KeyPair::new("test_keypair6");
    let network = networks::gnosis();
    let wallet = EthereumWallet::from_keypair(&keypair, network.clone()).unwrap();
    
    // Create a provider
    let provider_result = create_provider(&network);
    assert!(provider_result.is_ok());
    
    // We can't actually test send_eth without a blockchain
    // In a real test, we would mock the provider and test the function
}
