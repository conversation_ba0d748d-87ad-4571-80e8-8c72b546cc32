//! Tests for Ethereum network functionality.

use crate::vault::ethereum::*;

#[test]
fn test_network_config() {
    let gnosis = networks::gnosis();
    assert_eq!(gnosis.name, "Gnosis");
    assert_eq!(gnosis.chain_id, 100);
    assert_eq!(gnosis.token_symbol, "xDAI");
    
    let peaq = networks::peaq();
    assert_eq!(peaq.name, "Peaq");
    assert_eq!(peaq.chain_id, 3338);
    assert_eq!(peaq.token_symbol, "PEAQ");
    
    let agung = networks::agung();
    assert_eq!(agung.name, "Agung");
    assert_eq!(agung.chain_id, 9990);
    assert_eq!(agung.token_symbol, "AGNG");
}

#[test]
fn test_network_registry() {
    let network_names = networks::list_network_names();
    assert!(network_names.iter().any(|&name| name == "Gnosis"));
    assert!(network_names.iter().any(|&name| name == "Peaq"));
    assert!(network_names.iter().any(|&name| name == "Agung"));
    
    let gnosis_proper = networks::get_proper_network_name("gnosis");
    assert_eq!(gnosis_proper, Some("Gnosis"));
    
    let peaq_proper = networks::get_proper_network_name("peaq");
    assert_eq!(peaq_proper, Some("Peaq"));
    
    let agung_proper = networks::get_proper_network_name("agung");
    assert_eq!(agung_proper, Some("Agung"));
    
    let unknown = networks::get_proper_network_name("unknown");
    assert_eq!(unknown, None);
    
    let gnosis_config = networks::get_network_by_name("Gnosis");
    assert!(gnosis_config.is_some());
    assert_eq!(gnosis_config.unwrap().chain_id, 100);
    
    let unknown_config = networks::get_network_by_name("Unknown");
    assert!(unknown_config.is_none());
}

#[test]
fn test_create_provider() {
    let gnosis = networks::gnosis();
    let peaq = networks::peaq();
    let agung = networks::agung();
    
    // Create providers
    let gnosis_provider = create_provider(&gnosis);
    let peaq_provider = create_provider(&peaq);
    let agung_provider = create_provider(&agung);
    
    // They should all succeed
    assert!(gnosis_provider.is_ok());
    assert!(peaq_provider.is_ok());
    assert!(agung_provider.is_ok());
    
    // The convenience functions should also work
    let gnosis_provider2 = create_gnosis_provider();
    let peaq_provider2 = create_peaq_provider();
    let agung_provider2 = create_agung_provider();
    
    assert!(gnosis_provider2.is_ok());
    assert!(peaq_provider2.is_ok());
    assert!(agung_provider2.is_ok());
}
