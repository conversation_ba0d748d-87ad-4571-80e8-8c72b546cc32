//! Tests for smart contract functionality.

use ethers::types::Address;
use std::str::FromStr;

use crate::vault::ethereum::*;

#[test]
fn test_contract_creation() {
    // Create a simple ABI
    let abi_json = r#"[
        {
            "inputs": [],
            "name": "getValue",
            "outputs": [{"type": "uint256", "name": ""}],
            "stateMutability": "view",
            "type": "function"
        },
        {
            "inputs": [{"type": "uint256", "name": "newValue"}],
            "name": "setValue",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }
    ]"#;

    // Parse the ABI
    let abi = load_abi_from_json(abi_json).unwrap();

    // Create a contract address
    let address = Address::from_str("******************************************").unwrap();

    // Create a network config
    let network = networks::gnosis();

    // Create a contract
    let contract = Contract::new(address, abi, network);

    // Verify the contract was created correctly
    assert_eq!(contract.address, address);
    assert_eq!(contract.network.name, "Gnosis");

    // Verify the ABI contains the expected functions
    assert!(contract.abi.function("getValue").is_ok());
    assert!(contract.abi.function("setValue").is_ok());
}

#[test]
fn test_contract_from_address_string() {
    // Create a simple ABI
    let abi_json = r#"[
        {
            "inputs": [],
            "name": "getValue",
            "outputs": [{"type": "uint256", "name": ""}],
            "stateMutability": "view",
            "type": "function"
        }
    ]"#;

    // Parse the ABI
    let abi = load_abi_from_json(abi_json).unwrap();

    // Create a network config
    let network = networks::gnosis();

    // Create a contract from an address string
    let address_str = "******************************************";
    let contract = Contract::from_address_string(address_str, abi, network).unwrap();

    // Verify the contract was created correctly
    assert_eq!(contract.address, Address::from_str(address_str).unwrap());

    // Test with an invalid address
    let invalid_address = "0xinvalid";
    let result = Contract::from_address_string(invalid_address, contract.abi.clone(), contract.network.clone());
    assert!(result.is_err());
}

// Note: We can't easily test the actual contract calls in unit tests without mocking
// the provider, which would be complex. These would be better tested in integration tests
// with a local blockchain or testnet.
