//! Ethereum wallet functionality
//!
//! This module provides functionality for creating and managing Ethereum wallets
//! and interacting with smart contracts on EVM-based blockchains.
//!
//! The module is organized into several components:
//! - `wallet.rs`: Core Ethereum wallet implementation
//! - `networks.rs`: Network registry and configuration
//! - `provider.rs`: Provider creation and management
//! - `transaction.rs`: Transaction-related functionality
//! - `storage.rs`: Wallet storage functionality
//! - `contract.rs`: Smart contract interaction functionality

mod wallet;
mod provider;
mod transaction;
mod storage;
mod contract;
pub mod contract_utils;
pub mod networks;
// Re-export public types and functions
pub use wallet::EthereumWallet;
pub use networks::NetworkConfig;

// Re-export wallet creation functions
pub use storage::{
    create_ethereum_wallet_for_network,
    create_peaq_wallet,
    create_agung_wallet,
    create_ethereum_wallet_from_name_for_network,
    create_ethereum_wallet_from_name,
    create_ethereum_wallet_from_private_key_for_network,
    create_ethereum_wallet_from_private_key,
};

// Re-export wallet management functions
pub use storage::{
    get_current_ethereum_wallet_for_network,
    get_current_peaq_wallet,
    get_current_agung_wallet,
    clear_ethereum_wallets,
    clear_ethereum_wallets_for_network,
};

// Re-export provider functions
pub use provider::{
    create_provider,
    create_gnosis_provider,
    create_peaq_provider,
    create_agung_provider,
};

// Re-export transaction functions
pub use transaction::{
    get_balance,
    send_eth,
    format_balance,
};

// Re-export network registry functions
pub use networks::{
    get_network_by_name,
    get_proper_network_name,
    list_network_names,
    get_all_networks,
    names,
};

// Re-export contract functions
pub use contract::{
    Contract,
    load_abi_from_json,
    call_read_function,
    call_write_function,
    estimate_gas,
};

// Re-export contract utility functions
pub use contract_utils::{
    convert_rhai_to_token,
    prepare_function_arguments,
    convert_token_to_rhai,
    token_to_dynamic,
};
