//! Ethereum network registry
//!
//! This module provides a centralized registry of Ethereum networks and utilities
//! to work with them.

use std::collections::HashMap;
use std::sync::OnceLock;
use serde::{Serialize, Deserialize};

/// Configuration for an EVM-compatible network
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub name: String,
    pub chain_id: u64,
    pub rpc_url: String,
    pub explorer_url: String,
    pub token_symbol: String,
    pub decimals: u8,
}

/// Network name constants
pub mod names {
    pub const GNOSIS: &str = "Gnosis";
    pub const PEAQ: &str = "Peaq";
    pub const AGUNG: &str = "Agung";
}

/// Get the Gnosis Chain network configuration
pub fn gnosis() -> NetworkConfig {
    NetworkConfig {
        name: names::GNOSIS.to_string(),
        chain_id: 100,
        rpc_url: "https://rpc.gnosischain.com".to_string(),
        explorer_url: "https://gnosisscan.io".to_string(),
        token_symbol: "xDAI".to_string(),
        decimals: 18,
    }
}

/// Get the Peaq Network configuration
pub fn peaq() -> NetworkConfig {
    NetworkConfig {
        name: names::PEAQ.to_string(),
        chain_id: 3338,
        rpc_url: "https://peaq.api.onfinality.io/public".to_string(),
        explorer_url: "https://peaq.subscan.io/".to_string(),
        token_symbol: "PEAQ".to_string(),
        decimals: 18,
    }
}

/// Get the Agung Testnet configuration
pub fn agung() -> NetworkConfig {
    NetworkConfig {
        name: names::AGUNG.to_string(),
        chain_id: 9990,
        rpc_url: "https://wss-async.agung.peaq.network".to_string(),
        explorer_url: "https://agung-testnet.subscan.io/".to_string(),
        token_symbol: "AGNG".to_string(),
        decimals: 18,
    }
}

/// Get a network by its name (case-insensitive)
pub fn get_network_by_name(name: &str) -> Option<NetworkConfig> {
    let name_lower = name.to_lowercase();
    match name_lower.as_str() {
        "gnosis" => Some(gnosis()),
        "peaq" => Some(peaq()),
        "agung" => Some(agung()),
        _ => None,
    }
}

/// Get the proper capitalization of a network name
pub fn get_proper_network_name(name: &str) -> Option<&'static str> {
    let name_lower = name.to_lowercase();
    match name_lower.as_str() {
        "gnosis" => Some(names::GNOSIS),
        "peaq" => Some(names::PEAQ),
        "agung" => Some(names::AGUNG),
        _ => None,
    }
}

/// Get a list of all supported network names
pub fn list_network_names() -> Vec<&'static str> {
    vec![names::GNOSIS, names::PEAQ, names::AGUNG]
}

/// Get a map of all networks
pub fn get_all_networks() -> &'static HashMap<&'static str, NetworkConfig> {
    static NETWORKS: OnceLock<HashMap<&'static str, NetworkConfig>> = OnceLock::new();

    NETWORKS.get_or_init(|| {
        let mut map = HashMap::new();
        map.insert(names::GNOSIS, gnosis());
        map.insert(names::PEAQ, peaq());
        map.insert(names::AGUNG, agung());
        map
    })
}
