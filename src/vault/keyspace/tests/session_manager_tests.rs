use crate::vault::keyspace::keypair_types::KeySpace;
use crate::vault::keyspace::session_manager::{
    clear_session, create_keypair, create_space, get_current_space, get_selected_keypair,
    list_keypairs, select_keypair, set_current_space,
};

// Helper function to clear the session before each test
fn setup_test() {
    clear_session();
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_and_get_space() {
        setup_test();
        create_space("test_space").expect("Failed to create space");
        let space = get_current_space().expect("Failed to get current space");
        assert_eq!(space.name, "test_space");
    }

    #[test]
    fn test_set_current_space() {
        setup_test();
        let space = KeySpace::new("another_space");
        set_current_space(space.clone()).expect("Failed to set current space");
        let current_space = get_current_space().expect("Failed to get current space");
        assert_eq!(current_space.name, "another_space");
    }

    #[test]
    fn test_clear_session() {
        setup_test();
        create_space("test_space").expect("Failed to create space");
        clear_session();
        let result = get_current_space();
        assert!(result.is_err());
    }

    #[test]
    fn test_create_and_select_keypair() {
        setup_test();
        create_space("test_space").expect("Failed to create space");
        create_keypair("test_keypair").expect("Failed to create keypair");
        let keypair = get_selected_keypair().expect("Failed to get selected keypair");
        assert_eq!(keypair.name, "test_keypair");

        select_keypair("test_keypair").expect("Failed to select keypair");
        let selected_keypair =
            get_selected_keypair().expect("Failed to get selected keypair after select");
        assert_eq!(selected_keypair.name, "test_keypair");
    }

    #[test]
    fn test_list_keypairs() {
        setup_test();
        create_space("test_space").expect("Failed to create space");
        create_keypair("keypair1").expect("Failed to create keypair1");
        create_keypair("keypair2").expect("Failed to create keypair2");

        let keypairs = list_keypairs().expect("Failed to list keypairs");
        assert_eq!(keypairs.len(), 2);
        assert!(keypairs.contains(&"keypair1".to_string()));
        assert!(keypairs.contains(&"keypair2".to_string()));
    }

    #[test]
    fn test_create_keypair_no_active_space() {
        setup_test();
        let result = create_keypair("test_keypair");
        assert!(result.is_err());
    }

    #[test]
    fn test_select_keypair_no_active_space() {
        setup_test();
        let result = select_keypair("test_keypair");
        assert!(result.is_err());
    }

    #[test]
    fn test_select_nonexistent_keypair() {
        setup_test();
        create_space("test_space").expect("Failed to create space");
        let result = select_keypair("nonexistent_keypair");
        assert!(result.is_err());
    }

    #[test]
    fn test_get_selected_keypair_no_active_space() {
        setup_test();
        let result = get_selected_keypair();
        assert!(result.is_err());
    }

    #[test]
    fn test_get_selected_keypair_no_keypair_selected() {
        setup_test();
        create_space("test_space").expect("Failed to create space");
        let result = get_selected_keypair();
        assert!(result.is_err());
    }

    #[test]
    fn test_list_keypairs_no_active_space() {
        setup_test();
        let result = list_keypairs();
        assert!(result.is_err());
    }
}
