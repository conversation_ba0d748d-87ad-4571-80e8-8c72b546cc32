use crate::vault::keyspace::keypair_types::{KeyPair, KeySpace};

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_keypair_creation() {
        let keypair = KeyPair::new("test_keypair");
        assert_eq!(keypair.name, "test_keypair");
        // Basic check that keys are generated (they should have non-zero length)
        assert!(!keypair.pub_key().is_empty());
    }

    #[test]
    fn test_keypair_sign_and_verify() {
        let keypair = KeyPair::new("test_keypair");
        let message = b"This is a test message";
        let signature = keypair.sign(message);
        assert!(!signature.is_empty());

        let is_valid = keypair
            .verify(message, &signature)
            .expect("Verification failed");
        assert!(is_valid);

        // Test with a wrong message
        let wrong_message = b"This is a different message";
        let is_valid_wrong = keypair
            .verify(wrong_message, &signature)
            .expect("Verification failed with wrong message");
        assert!(!is_valid_wrong);
    }

    #[test]
    fn test_verify_with_public_key() {
        let keypair = KeyPair::new("test_keypair");
        let message = b"Another test message";
        let signature = keypair.sign(message);
        let public_key = keypair.pub_key();

        let is_valid = KeyPair::verify_with_public_key(&public_key, message, &signature)
            .expect("Verification with public key failed");
        assert!(is_valid);

        // Test with a wrong public key
        let wrong_keypair = KeyPair::new("wrong_keypair");
        let wrong_public_key = wrong_keypair.pub_key();
        let is_valid_wrong_key =
            KeyPair::verify_with_public_key(&wrong_public_key, message, &signature)
                .expect("Verification with wrong public key failed");
        assert!(!is_valid_wrong_key);
    }

    #[test]
    fn test_asymmetric_encryption_decryption() {
        // Sender's keypair
        let sender_keypair = KeyPair::new("sender");
        let _ = sender_keypair.pub_key();

        // Recipient's keypair
        let recipient_keypair = KeyPair::new("recipient");
        let recipient_public_key = recipient_keypair.pub_key();

        let message = b"This is a secret message";

        // Sender encrypts for recipient
        let ciphertext = sender_keypair
            .encrypt_asymmetric(&recipient_public_key, message)
            .expect("Encryption failed");
        assert!(!ciphertext.is_empty());

        // Recipient decrypts
        let decrypted_message = recipient_keypair
            .decrypt_asymmetric(&ciphertext)
            .expect("Decryption failed");
        assert_eq!(decrypted_message, message);

        // Test decryption with wrong keypair
        let wrong_keypair = KeyPair::new("wrong_recipient");
        let result = wrong_keypair.decrypt_asymmetric(&ciphertext);
        assert!(result.is_err());
    }

    #[test]
    fn test_keyspace_add_keypair() {
        let mut space = KeySpace::new("test_space");
        space
            .add_keypair("keypair1")
            .expect("Failed to add keypair1");
        assert_eq!(space.keypairs.len(), 1);
        assert!(space.keypairs.contains_key("keypair1"));

        // Test adding a duplicate keypair
        let result = space.add_keypair("keypair1");
        assert!(result.is_err());
    }
}
