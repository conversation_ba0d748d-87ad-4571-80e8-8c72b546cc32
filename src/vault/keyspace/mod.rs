//! Key pair management functionality
//!
//! This module provides functionality for creating and managing ECDSA key pairs.

pub mod keypair_types;
pub mod session_manager;

// Re-export public types and functions
pub use keypair_types::{KeyPair, KeySpace};
pub use session_manager::{
    create_space, set_current_space, get_current_space, clear_session,
    create_keypair, select_keypair, get_selected_keypair, list_keypairs,
    keypair_pub_key, derive_public_key, keypair_sign, keypair_verify,
    verify_with_public_key, encrypt_asymmetric, decrypt_asymmetric
};

#[cfg(test)]
mod tests;
