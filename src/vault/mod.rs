//! Hero Vault: Cryptographic functionality for SAL
//!
//! This module provides cryptographic operations including:
//! - Key space management (creation, loading, encryption, decryption)
//! - Key pair management (ECDSA)
//! - Digital signatures (signing and verification)
//! - Symmetric encryption (ChaCha20Poly1305)
//! - Ethereum wallet functionality
//! - Key-value store with encryption

pub mod error;
pub mod keyspace;
pub mod symmetric;
pub mod ethereum;
pub mod kvs;

// Re-export modules
// Re-export common types for convenience
pub use error::CryptoError;
pub use keyspace::{KeyPair, KeySpace};
