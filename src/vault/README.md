# Hero Vault Cryptography Module

The Hero Vault module provides comprehensive cryptographic functionality for the SAL project, including key management, digital signatures, symmetric encryption, Ethereum wallet operations, and a secure key-value store.

## Module Structure

The Hero Vault module is organized into several submodules:

- `error.rs` - Error types for cryptographic operations
- `keypair/` - ECDSA keypair management functionality
- `symmetric/` - Symmetric encryption using ChaCha20Poly1305
- `ethereum/` - Ethereum wallet and smart contract functionality
- `kvs/` - Encrypted key-value store

## Key Features

### Key Space Management

The module provides functionality for creating, loading, and managing key spaces. A key space is a secure container for cryptographic keys, which can be encrypted and stored on disk.

```rust
// Create a new key space
let space = KeySpace::new("my_space", "secure_password")?;

// Save the key space to disk
space.save()?;

// Load a key space from disk
let loaded_space = KeySpace::load("my_space", "secure_password")?;
```

### Keypair Management

The module provides functionality for creating, selecting, and using ECDSA keypairs for digital signatures.

```rust
// Create a new keypair in the active key space
let keypair = space.create_keypair("my_keypair", "secure_password")?;

// Select a keypair for use
space.select_keypair("my_keypair")?;

// List all keypairs in the active key space
let keypairs = space.list_keypairs()?;
```

### Digital Signatures

The module provides functionality for signing and verifying messages using ECDSA.

```rust
// Sign a message using the selected keypair
let signature = space.sign("This is a message to sign")?;

// Verify a signature
let is_valid = space.verify("This is a message to sign", &signature)?;
```

### Symmetric Encryption

The module provides functionality for symmetric encryption using ChaCha20Poly1305.

```rust
// Generate a new symmetric key
let key = space.generate_key()?;

// Encrypt a message
let encrypted = space.encrypt(&key, "This is a secret message")?;

// Decrypt a message
let decrypted = space.decrypt(&key, &encrypted)?;
```

### Ethereum Wallet Functionality

The module provides comprehensive Ethereum wallet functionality, including:

- Creating and managing wallets for different networks
- Sending ETH transactions
- Checking balances
- Interacting with smart contracts

```rust
// Create an Ethereum wallet
let wallet = EthereumWallet::new(keypair)?;

// Get the wallet address
let address = wallet.get_address()?;

// Send ETH
let tx_hash = wallet.send_eth("0x1234...", "1000000000000000")?;

// Check balance
let balance = wallet.get_balance("0x1234...")?;
```

### Smart Contract Interactions

The module provides functionality for interacting with smart contracts on EVM-based blockchains.

```rust
// Load a contract ABI
let contract = Contract::new(provider, "0x1234...", abi)?;

// Call a read-only function
let result = contract.call_read("balanceOf", vec!["0x5678..."])?;

// Call a write function
let tx_hash = contract.call_write("transfer", vec!["0x5678...", "1000"])?;
```

### Key-Value Store

The module provides an encrypted key-value store for securely storing sensitive data.

```rust
// Create a new store
let store = KvStore::new("my_store", "secure_password")?;

// Set a value
store.set("api_key", "secret_api_key")?;

// Get a value
let api_key = store.get("api_key")?;
```

## Error Handling

The module uses a comprehensive error type (`CryptoError`) for handling errors that can occur during cryptographic operations:

- `InvalidKeyLength` - Invalid key length
- `EncryptionFailed` - Encryption failed
- `DecryptionFailed` - Decryption failed
- `SignatureFormatError` - Signature format error
- `KeypairAlreadyExists` - Keypair already exists
- `KeypairNotFound` - Keypair not found
- `NoActiveSpace` - No active key space
- `NoKeypairSelected` - No keypair selected
- `SerializationError` - Serialization error
- `InvalidAddress` - Invalid address format
- `ContractError` - Smart contract error

## Ethereum Networks

The module supports multiple Ethereum networks, including:

- Gnosis Chain
- Peaq Network
- Agung Network

## Security Considerations

- Key spaces are encrypted with ChaCha20Poly1305 using a key derived from the provided password
- Private keys are never stored in plaintext
- The module uses secure random number generation for key creation
- All cryptographic operations use well-established libraries and algorithms

## Examples

For examples of how to use the Hero Vault module, see the `examples/hero_vault` directory.
