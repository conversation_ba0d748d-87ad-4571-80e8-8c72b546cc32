# Hero Vault Key-Value Store Module

The Key-Value Store (KVS) module provides an encrypted key-value store for securely storing sensitive data.

## Module Structure

The KVS module is organized into:

- `store.rs` - Core implementation of the key-value store
- `error.rs` - Error types specific to the KVS module
- `mod.rs` - Module exports and public interface

## Key Types

### KvStore

The `KvStore` type represents an encrypted key-value store:

```rust
pub struct KvStore {
    // Private fields
    // ...
}

impl KvStore {
    // Create a new store
    pub fn new(name: &str, password: &str) -> Result<Self, CryptoError>;
    
    // Load a store from disk
    pub fn load(name: &str, password: &str) -> Result<Self, CryptoError>;
    
    // Save the store to disk
    pub fn save(&self) -> Result<(), CryptoError>;
    
    // Set a value
    pub fn set(&mut self, key: &str, value: &str) -> Result<(), CryptoError>;
    
    // Get a value
    pub fn get(&self, key: &str) -> Result<Option<String>, CryptoError>;
    
    // Delete a value
    pub fn delete(&mut self, key: &str) -> Result<(), CryptoError>;
    
    // Check if a key exists
    pub fn has(&self, key: &str) -> Result<bool, CryptoError>;
    
    // List all keys
    pub fn keys(&self) -> Result<Vec<String>, CryptoError>;
    
    // Clear all values
    pub fn clear(&mut self) -> Result<(), CryptoError>;
    
    // Get the name of the store
    pub fn name(&self) -> &str;
}
```

## Key Features

### Store Management

The module provides functionality for creating, loading, and managing key-value stores:

```rust
// Create a new store
let mut store = KvStore::new("my_store", "secure_password")?;

// Save the store to disk
store.save()?;

// Load a store from disk
let mut loaded_store = KvStore::load("my_store", "secure_password")?;
```

### Value Management

The module provides functionality for managing values in the store:

```rust
// Set a value
store.set("api_key", "secret_api_key")?;

// Get a value
let api_key = store.get("api_key")?;

// Delete a value
store.delete("api_key")?;

// Check if a key exists
let exists = store.has("api_key")?;

// List all keys
let keys = store.keys()?;

// Clear all values
store.clear()?;
```

## Technical Details

### Encryption

The KVS module uses the Symmetric Encryption module to encrypt all values stored in the key-value store. This ensures that sensitive data is protected at rest.

The encryption process:

1. A master key is derived from the provided password using PBKDF2
2. Each value is encrypted using ChaCha20Poly1305 with a unique key derived from the master key and the value's key
3. The encrypted values are stored in a JSON file on disk

### Storage Format

The key-value store is stored in a JSON file with the following structure:

```json
{
  "name": "my_store",
  "salt": "base64-encoded-salt",
  "values": {
    "key1": "base64-encoded-encrypted-value",
    "key2": "base64-encoded-encrypted-value",
    ...
  }
}
```

The file is stored in the `~/.hero-vault/stores/` directory by default.

## Security Considerations

- Use strong passwords to protect the key-value store
- The security of the store depends on the strength of the password
- Consider the security implications of storing sensitive data on disk
- Regularly backup the store to prevent data loss

## Error Handling

The module uses the `CryptoError` type for handling errors that can occur during key-value store operations:

- `EncryptionFailed` - Encryption failed
- `DecryptionFailed` - Decryption failed
- `SerializationError` - Serialization error

## Examples

For examples of how to use the KVS module, see the `examples/hero_vault` directory. While there may not be specific examples for the KVS module, the general pattern of usage is similar to the key space management examples.

A basic usage example:

```rust
// Create a new store
let mut store = KvStore::new("my_store", "secure_password")?;

// Set some values
store.set("api_key", "secret_api_key")?;
store.set("access_token", "secret_access_token")?;

// Save the store to disk
store.save()?;

// Later, load the store
let loaded_store = KvStore::load("my_store", "secure_password")?;

// Get a value
let api_key = loaded_store.get("api_key")?;
println!("API Key: {}", api_key.unwrap_or_default());
```

## to test

```bash
cargo test --lib vault::keypair
```