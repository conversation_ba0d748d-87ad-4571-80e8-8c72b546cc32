//! Error types for the key-value store.

use thiserror::Error;

/// Errors that can occur when using the key-value store.
#[derive(Debug, Error)]
pub enum KvsError {
    /// I/O error
    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),

    /// Key not found
    #[error("Key not found: {0}")]
    KeyNotFound(String),

    /// Store not found
    #[error("Store not found: {0}")]
    StoreNotFound(String),

    /// Serialization error
    #[error("Serialization error: {0}")]
    Serialization(String),

    /// Deserialization error
    #[error("Deserialization error: {0}")]
    Deserialization(String),

    /// Encryption error
    #[error("Encryption error: {0}")]
    Encryption(String),

    /// Decryption error
    #[error("Decryption error: {0}")]
    Decryption(String),

    /// Other error
    #[error("Error: {0}")]
    Other(String),
}

impl From<serde_json::Error> for KvsError {
    fn from(err: serde_json::Error) -> Self {
        KvsError::Serialization(err.to_string())
    }
}

impl From<KvsError> for crate::vault::error::CryptoError {
    fn from(err: KvsError) -> Self {
        crate::vault::error::CryptoError::SerializationError(err.to_string())
    }
}

impl From<crate::vault::error::CryptoError> for KvsError {
    fn from(err: crate::vault::error::CryptoError) -> Self {
        match err {
            crate::vault::error::CryptoError::EncryptionFailed(msg) => KvsError::Encryption(msg),
            crate::vault::error::CryptoError::DecryptionFailed(msg) => KvsError::Decryption(msg),
            crate::vault::error::CryptoError::SerializationError(msg) => {
                KvsError::Serialization(msg)
            }
            _ => KvsError::Other(err.to_string()),
        }
    }
}

/// Result type for key-value store operations.
pub type Result<T> = std::result::Result<T, KvsError>;
