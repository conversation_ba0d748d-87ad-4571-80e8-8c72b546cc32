use crate::vault::kvs::store::{create_store, delete_store, open_store};

// Helper function to generate a unique store name for each test
fn generate_test_store_name() -> String {
    use rand::Rng;
    let random_string: String = rand::thread_rng()
        .sample_iter(&rand::distributions::Alphanumeric)
        .take(10)
        .map(char::from)
        .collect();
    format!("test_store_{}", random_string)
}

// Helper function to clean up test stores
fn cleanup_test_store(name: &str) {
    let _ = delete_store(name);
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_and_open_store() {
        let store_name = generate_test_store_name();
        let store = create_store(&store_name, false, None).expect("Failed to create store");
        assert_eq!(store.name(), store_name);
        assert!(!store.is_encrypted());

        let opened_store = open_store(&store_name, None).expect("Failed to open store");
        assert_eq!(opened_store.name(), store_name);
        assert!(!opened_store.is_encrypted());

        cleanup_test_store(&store_name);
    }

    #[test]
    fn test_set_and_get_value() {
        let store_name = generate_test_store_name();
        let store = create_store(&store_name, false, None).expect("Failed to create store");

        store.set("key1", &"value1").expect("Failed to set value");
        let value: String = store.get("key1").expect("Failed to get value");
        assert_eq!(value, "value1");

        cleanup_test_store(&store_name);
    }

    #[test]
    fn test_delete_value() {
        let store_name = generate_test_store_name();
        let store = create_store(&store_name, false, None).expect("Failed to create store");

        store.set("key1", &"value1").expect("Failed to set value");
        store.delete("key1").expect("Failed to delete value");
        let result: Result<String, _> = store.get("key1");
        assert!(result.is_err());

        cleanup_test_store(&store_name);
    }

    #[test]
    fn test_contains_key() {
        let store_name = generate_test_store_name();
        let store = create_store(&store_name, false, None).expect("Failed to create store");

        store.set("key1", &"value1").expect("Failed to set value");
        assert!(store.contains("key1").expect("Failed to check contains"));
        assert!(!store.contains("key2").expect("Failed to check contains"));

        cleanup_test_store(&store_name);
    }

    #[test]
    fn test_list_keys() {
        let store_name = generate_test_store_name();
        let store = create_store(&store_name, false, None).expect("Failed to create store");

        store.set("key1", &"value1").expect("Failed to set value");
        store.set("key2", &"value2").expect("Failed to set value");

        let keys = store.keys().expect("Failed to list keys");
        assert_eq!(keys.len(), 2);
        assert!(keys.contains(&"key1".to_string()));
        assert!(keys.contains(&"key2".to_string()));

        cleanup_test_store(&store_name);
    }

    #[test]
    fn test_clear_store() {
        let store_name = generate_test_store_name();
        let store = create_store(&store_name, false, None).expect("Failed to create store");

        store.set("key1", &"value1").expect("Failed to set value");
        store.set("key2", &"value2").expect("Failed to set value");

        store.clear().expect("Failed to clear store");
        let keys = store.keys().expect("Failed to list keys after clear");
        assert!(keys.is_empty());

        cleanup_test_store(&store_name);
    }
}
