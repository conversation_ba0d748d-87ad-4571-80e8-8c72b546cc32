mod containers;
mod images;
mod cmd;
mod builder;
mod content;
#[cfg(test)]
mod containers_test;

use std::fmt;
use std::error::Error;
use std::io;

/// Error type for buildah operations
#[derive(Debug)]
pub enum BuildahError {
    /// The buildah command failed to execute
    CommandExecutionFailed(io::Error),
    /// The buildah command executed but returned an error
    CommandFailed(String),
    /// Failed to parse JSON output
    JsonParseError(String),
    /// Failed to convert data
    ConversionError(String),
    /// Generic error
    Other(String),
}

impl fmt::Display for BuildahError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            BuildahError::CommandExecutionFailed(e) => write!(f, "Failed to execute buildah command: {}", e),
            BuildahError::CommandFailed(e) => write!(f, "Buildah command failed: {}", e),
            BuildahError::JsonParseError(e) => write!(f, "Failed to parse JSON: {}", e),
            BuildahError::ConversionError(e) => write!(f, "Conversion error: {}", e),
            BuildahError::Other(e) => write!(f, "{}", e),
        }
    }
}

impl Error for BuildahError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        match self {
            BuildahError::CommandExecutionFailed(e) => Some(e),
            _ => None,
        }
    }
}
// Re-export the Builder
pub use builder::Builder;

// Re-export existing functions for backward compatibility
#[deprecated(since = "0.2.0", note = "Use Builder::new() instead")]
pub use containers::*;
#[deprecated(since = "0.2.0", note = "Use Builder methods instead")]
pub use images::*;
pub use cmd::*;
pub use content::ContentOperations;