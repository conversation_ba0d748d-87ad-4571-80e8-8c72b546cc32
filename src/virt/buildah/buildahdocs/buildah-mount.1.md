# buildah-mount "1" "March 2017" "buildah"

## NAME
buildah\-mount - Mount a working container's root filesystem.

## SYNOPSIS
**buildah mount** [*container* ...]

## DESCRIPTION
Mounts the specified container's root file system in a location which can be
accessed from the host, and returns its location.

If the mount command is invoked without any arguments, the tool will list all of the currently mounted containers.

When running in rootless mode, mount runs in a different namespace so
that the mounted volume might not be accessible from the host when
using a driver different than `vfs`.  To be able to access the file
system mounted, you might need to create the mount namespace
separately as part of `buildah unshare`.  In the environment created
with `buildah unshare` you can then use `buildah mount` and have
access to the mounted file system.

## RETURN VALUE
The location of the mounted file system.  On error an empty string and errno is
returned.

## OPTIONS

**--json**

Output in JSON format.

## EXAMPLE

```
buildah mount working-container
/var/lib/containers/storage/overlay2/f3ac502d97b5681989dff84dfedc8354239bcecbdc2692f9a639f4e080a02364/merged
```

```
buildah mount
working-container /var/lib/containers/storage/overlay2/f3ac502d97b5681989dff84dfedc8354239bcecbdc2692f9a639f4e080a02364/merged
fedora-working-container /var/lib/containers/storage/overlay2/0ff7d7ca68bed1ace424f9df154d2dd7b5a125c19d887f17653cbcd5b6e30ba1/merged
```

```
buildah mount working-container fedora-working-container ubi8-working-container
working-container /var/lib/containers/storage/overlay/f8cac5cce73e5102ab321cc5b57c0824035b5cb82b6822e3c86ebaff69fefa9c/merged
fedora-working-container /var/lib/containers/storage/overlay/c3ec418be5bda5b72dca74c4d397e05829fe62ecd577dd7518b5f7fc1ca5f491/merged
ubi8-working-container /var/lib/containers/storage/overlay/03a071f206f70f4fcae5379bd5126be86b5352dc2a0c3449cd6fca01b77ea868/merged
```

If running in rootless mode, you need to do a buildah unshare first to use
the mount point.
```
$ buildah unshare
# buildah mount working-container
/var/lib/containers/storage/overlay/f8cac5cce73e5102ab321cc5b57c0824035b5cb82b6822e3c86ebaff69fefa9c/merged
# cp foobar  /var/lib/containers/storage/overlay/f8cac5cce73e5102ab321cc5b57c0824035b5cb82b6822e3c86ebaff69fefa9c/merged
# buildah unmount working-container
# exit
$ buildah commit  working-container newimage
```

## SEE ALSO
buildah(1), buildah-unshare(1), buildah-umount(1)
