// File: /root/code/git.threefold.info/herocode/sal/src/virt/nerdctl/cmd.rs

// Basic nerdctl operations for container management
use std::process::Command;
use crate::process::CommandResult;
use super::NerdctlError;

/// Execute a nerdctl command and return the result
pub fn execute_nerdctl_command(args: &[&str]) -> Result<CommandResult, NerdctlError> {
    let output = Command::new("nerdctl")
        .args(args)
        .output();
    
    match output {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout).to_string();
            let stderr = String::from_utf8_lossy(&output.stderr).to_string();
            
            let result = CommandResult {
                stdout,
                stderr,
                success: output.status.success(),
                code: output.status.code().unwrap_or(-1),
            };
            
            if result.success {
                Ok(result)
            } else {
                Err(NerdctlError::CommandFailed(format!("Command failed with code {}: {}", 
                    result.code, result.stderr.trim())))
            }
        },
        Err(e) => {
            Err(NerdctlError::CommandExecutionFailed(e))
        }
    }
}