// File: /root/code/git.threefold.info/herocode/sal/src/virt/nerdctl/container_types.rs

use std::collections::HashMap;

/// Container struct for nerdctl operations
#[derive(Clone)]
pub struct Container {
    /// Name of the container
    pub name: String,
    /// Container ID
    pub container_id: Option<String>,
    /// Base image (if created from an image)
    pub image: Option<String>,
    /// Configuration options
    pub config: HashMap<String, String>,
    /// Port mappings
    pub ports: Vec<String>,
    /// Volume mounts
    pub volumes: Vec<String>,
    /// Environment variables
    pub env_vars: HashMap<String, String>,
    /// Network to connect to
    pub network: Option<String>,
    /// Network aliases
    pub network_aliases: Vec<String>,
    /// CPU limit
    pub cpu_limit: Option<String>,
    /// Memory limit
    pub memory_limit: Option<String>,
    /// Memory swap limit
    pub memory_swap_limit: Option<String>,
    /// CPU shares
    pub cpu_shares: Option<String>,
    /// Restart policy
    pub restart_policy: Option<String>,
    /// Health check
    pub health_check: Option<HealthCheck>,
    /// Whether to run in detached mode
    pub detach: bool,
    /// Snapshotter to use
    pub snapshotter: Option<String>,
}

/// Health check configuration for a container
#[derive(Debug, Clone)]
pub struct HealthCheck {
    /// Command to run for health check
    pub cmd: String,
    /// Time between running the check (default: 30s)
    pub interval: Option<String>,
    /// Maximum time to wait for a check to complete (default: 30s)
    pub timeout: Option<String>,
    /// Number of consecutive failures needed to consider unhealthy (default: 3)
    pub retries: Option<u32>,
    /// Start period for the container to initialize before counting retries (default: 0s)
    pub start_period: Option<String>,
}

/// Container status information
#[derive(Debug, Clone)]
pub struct ContainerStatus {
    /// Container state (e.g., running, stopped)
    pub state: String,
    /// Container status
    pub status: String,
    /// Creation time
    pub created: String,
    /// Start time
    pub started: String,
    /// Health status (if health check is configured)
    pub health_status: Option<String>,
    /// Health check output (if health check is configured)
    pub health_output: Option<String>,
}

/// Container resource usage information
#[derive(Debug, Clone)]
pub struct ResourceUsage {
    /// CPU usage percentage
    pub cpu_usage: String,
    /// Memory usage
    pub memory_usage: String,
    /// Memory limit
    pub memory_limit: String,
    /// Memory usage percentage
    pub memory_percentage: String,
    /// Network input
    pub network_input: String,
    /// Network output
    pub network_output: String,
    /// Block input
    pub block_input: String,
    /// Block output
    pub block_output: String,
    /// PIDs
    pub pids: String,
}