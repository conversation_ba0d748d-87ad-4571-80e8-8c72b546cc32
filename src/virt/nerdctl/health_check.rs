// File: /root/code/git.threefold.info/herocode/sal/src/virt/nerdctl/health_check.rs

use super::container_types::HealthCheck;

impl HealthCheck {
    /// Create a new health check with the given command
    pub fn new(cmd: &str) -> Self {
        Self {
            cmd: cmd.to_string(),
            interval: None,
            timeout: None,
            retries: None,
            start_period: None,
        }
    }
    
    /// Set the interval between health checks
    pub fn with_interval(mut self, interval: &str) -> Self {
        self.interval = Some(interval.to_string());
        self
    }
    
    /// Set the timeout for health checks
    pub fn with_timeout(mut self, timeout: &str) -> Self {
        self.timeout = Some(timeout.to_string());
        self
    }
    
    /// Set the number of retries for health checks
    pub fn with_retries(mut self, retries: u32) -> Self {
        self.retries = Some(retries);
        self
    }
    
    /// Set the start period for health checks
    pub fn with_start_period(mut self, start_period: &str) -> Self {
        self.start_period = Some(start_period.to_string());
        self
    }
}