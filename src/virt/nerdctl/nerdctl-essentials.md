# nerdctl Essentials

This guide provides a comprehensive overview of essential nerdctl functionality to help you get started quickly. nerdctl is a Docker-compatible CLI for containerd, with additional features specifically designed for containerd environments.

## Introduction

nerdctl is a Docker-compatible CLI for containerd. It provides the same user experience as the Docker CLI (`docker`) but leverages the more efficient containerd container runtime. Key differences and advantages include:

- Direct integration with containerd (no extra daemon required)
- Support for containerd-specific features
- First-class support for rootless mode
- Compatibility with Docker commands
- Additional nerdctl-specific commands

## Basic Configuration

nerdctl can be configured using the `nerdctl.toml` configuration file:

- Rootful mode: `/etc/nerdctl/nerdctl.toml`
- Rootless mode: `~/.config/nerdctl/nerdctl.toml`

Example configuration:

```toml
debug = false
debug_full = false
address = "unix:///run/containerd/containerd.sock"
namespace = "default"
snapshotter = "overlayfs"
cgroup_manager = "systemd"
hosts_dir = ["/etc/containerd/certs.d", "/etc/docker/certs.d"]
```

Common configuration properties:

| Property            | CLI Flag                          | Description                |
|---------------------|-----------------------------------|----------------------------|
| `address`           | `--address`, `--host`, `-a`, `-H` | containerd address         |
| `namespace`         | `--namespace`, `-n`               | containerd namespace       |
| `snapshotter`       | `--snapshotter`                   | containerd snapshotter     |
| `cni_path`          | `--cni-path`                      | CNI binary directory       |
| `data_root`         | `--data-root`                     | Persistent state directory |
| `insecure_registry` | `--insecure-registry`             | Allow insecure registry    |

## Container Management

### Running Containers

**Run a container**:
```
nerdctl run [OPTIONS] IMAGE [COMMAND] [ARG...]
```

Common options:
- `-i, --interactive`: Keep STDIN open
- `-t, --tty`: Allocate a pseudo-TTY
- `-d, --detach`: Run container in background
- `--name`: Assign a name to the container
- `-p, --publish`: Publish container's port to the host
- `-v, --volume`: Bind mount a volume
- `-e, --env`: Set environment variables
- `--rm`: Automatically remove the container when it exits
- `--restart=(no|always|on-failure|unless-stopped)`: Restart policy
- `--net, --network`: Connect container to a network

Examples:
```bash
# Run an interactive container and automatically remove it when it exits
nerdctl run -it --rm alpine sh

# Run a detached container with port mapping
nerdctl run -d --name nginx -p 8080:80 nginx

# Run a container with a volume mount
nerdctl run -it --rm -v $(pwd):/data alpine ls /data
```

### Managing Containers

**List containers**:
```
nerdctl ps [OPTIONS]
```

Options:
- `-a, --all`: Show all containers (default shows just running)
- `-q, --quiet`: Only display container IDs
- `-s, --size`: Display total file sizes

**Stop a container**:
```
nerdctl stop [OPTIONS] CONTAINER [CONTAINER...]
```

**Start a container**:
```
nerdctl start [OPTIONS] CONTAINER [CONTAINER...]
```

**Remove a container**:
```
nerdctl rm [OPTIONS] CONTAINER [CONTAINER...]
```

Options:
- `-f, --force`: Force removal of running container
- `-v, --volumes`: Remove anonymous volumes

**View container logs**:
```
nerdctl logs [OPTIONS] CONTAINER
```

Options:
- `-f, --follow`: Follow log output
- `--since`: Show logs since timestamp
- `-t, --timestamps`: Show timestamps
- `-n, --tail`: Number of lines to show from the end of logs

**Execute a command in a running container**:
```
nerdctl exec [OPTIONS] CONTAINER COMMAND [ARG...]
```

Options:
- `-i, --interactive`: Keep STDIN open
- `-t, --tty`: Allocate a pseudo-TTY
- `-d, --detach`: Detached mode
- `-w, --workdir`: Working directory
- `-e, --env`: Set environment variables

## Image Management

### Working with Images

**List images**:
```
nerdctl images [OPTIONS]
```

Options:
- `-a, --all`: Show all images
- `-q, --quiet`: Only show numeric IDs
- `--digests`: Show digests

**Pull an image**:
```
nerdctl pull [OPTIONS] NAME[:TAG|@DIGEST]
```

Options:
- `--platform=(amd64|arm64|...)`: Pull content for specific platform
- `-q, --quiet`: Suppress verbose output

**Push an image**:
```
nerdctl push [OPTIONS] NAME[:TAG]
```

**Build an image**:
```
nerdctl build [OPTIONS] PATH
```

Options:
- `-t, --tag`: Name and optionally tag the image
- `-f, --file`: Name of the Dockerfile
- `--build-arg`: Set build-time variables
- `--no-cache`: Do not use cache when building

**Remove an image**:
```
nerdctl rmi [OPTIONS] IMAGE [IMAGE...]
```

Options:
- `-f, --force`: Force removal

**Tag an image**:
```
nerdctl tag SOURCE_IMAGE[:TAG] TARGET_IMAGE[:TAG]
```

**Save an image to a tar archive**:
```
nerdctl save [OPTIONS] IMAGE [IMAGE...]
```

Options:
- `-o, --output`: Write to a file

**Load an image from a tar archive**:
```
nerdctl load [OPTIONS]
```

Options:
- `-i, --input`: Read from a tar archive file

## Network Management

### Working with Networks

**List networks**:
```
nerdctl network ls [OPTIONS]
```

**Create a network**:
```
nerdctl network create [OPTIONS] NETWORK
```

Common options:
- `-d, --driver=(bridge|macvlan|ipvlan)`: Driver to manage the network
- `--subnet`: Subnet in CIDR format (e.g., "********/16")
- `--gateway`: Gateway for the subnet
- `--ipam-driver=(default|host-local|dhcp)`: IP address management driver

**Remove a network**:
```
nerdctl network rm NETWORK [NETWORK...]
```

**Inspect a network**:
```
nerdctl network inspect [OPTIONS] NETWORK [NETWORK...]
```

**Prune networks**:
```
nerdctl network prune [OPTIONS]
```

### Network Types

nerdctl supports the following network types:
- `bridge` (default on Linux): Creates a bridge interface on the host
- `host`: Uses the host's network stack
- `none`: No networking
- `macvlan`: Connects container interfaces directly to host interfaces
- `ipvlan`: Similar to macvlan but shares host's IP address

Example creating a macvlan network:
```bash
nerdctl network create macnet --driver macvlan \
  --subnet=***********/24 \
  --gateway=*********** \
  -o parent=eth0
```

## Volume Management

### Working with Volumes

**List volumes**:
```
nerdctl volume ls [OPTIONS]
```

**Create a volume**:
```
nerdctl volume create [OPTIONS] [VOLUME]
```

**Remove a volume**:
```
nerdctl volume rm [OPTIONS] VOLUME [VOLUME...]
```

**Inspect a volume**:
```
nerdctl volume inspect [OPTIONS] VOLUME [VOLUME...]
```

**Prune volumes**:
```
nerdctl volume prune [OPTIONS]
```

### Volume Flags for Containers

Volume-related flags when running containers:
- `-v, --volume`: Bind mount a volume (format: `SRC:DST[:OPTIONS]`)
- `--mount`: Attach a filesystem mount to the container
- `--tmpfs`: Mount a tmpfs directory

Volume options:
- `rw`: Read/write (default)
- `ro`: Read-only
- `rro`: Recursive read-only (kernel >= 5.12)
- `shared`, `slave`, `private`: Non-recursive propagation
- `rshared`, `rslave`, `rprivate`: Recursive propagation

Examples:
```bash
# Mount a host directory
nerdctl run -it --rm -v /host/path:/container/path:ro alpine ls /container/path

# Use tmpfs
nerdctl run -it --rm --tmpfs /tmp:size=64m,exec alpine ls /tmp
```

## Compose

nerdctl includes Docker Compose compatibility, allowing you to define and run multi-container applications.

**Run Compose applications**:
```
nerdctl compose up [OPTIONS]
```

Options:
- `-d, --detach`: Run containers in the background
- `--build`: Build images before starting containers
- `--no-build`: Don't build images, even if they're missing
- `--force-recreate`: Force recreation of containers

**Stop Compose applications**:
```
nerdctl compose down [OPTIONS]
```

Options:
- `-v, --volumes`: Remove named volumes and anonymous volumes

**View Compose logs**:
```
nerdctl compose logs [OPTIONS] [SERVICE...]
```

Other Compose commands:
- `nerdctl compose build`: Build service images
- `nerdctl compose ps`: List containers
- `nerdctl compose pull`: Pull service images
- `nerdctl compose exec`: Execute a command in a running container
- `nerdctl compose restart`: Restart services

Example `compose.yml`:
```yaml
version: "3.8"
services:
  web:
    image: nginx
    ports:
      - "8080:80"
    volumes:
      - ./html:/usr/share/nginx/html
  db:
    image: postgres
    environment:
      POSTGRES_PASSWORD: example
    volumes:
      - db-data:/var/lib/postgresql/data

volumes:
  db-data:
```

## Rootless Mode

nerdctl supports rootless containers, allowing unprivileged users to create and manage containers. This provides better security isolation compared to running everything as root.

### Setup Rootless Mode

1. Install required dependencies (see https://rootlesscontaine.rs/getting-started/common/)
2. Set up rootless containerd:
   ```
   containerd-rootless-setuptool.sh install
   ```
3. Enable lingering for your user (to keep services running after logout):
   ```
   sudo loginctl enable-linger $(whoami)
   ```
4. For building images, install BuildKit in rootless mode:
   ```
   containerd-rootless-setuptool.sh install-buildkit
   ```

When running in rootless mode, nerdctl automatically uses the appropriate socket and configuration.

### Limitations and Considerations

- Resource limits require cgroup v2 and systemd
- By default, ports below 1024 cannot be published (use slirp4netns port driver or configure capabilities)
- Some file system operations might be restricted
- Network performance can be slower (consider using bypass4netns to improve performance)

## Registry Authentication

nerdctl uses the same authentication configuration as Docker, located in `${DOCKER_CONFIG}/config.json` (default: `$HOME/.docker/config.json`).

**Log in to a registry**:
```
nerdctl login [OPTIONS] [SERVER]
```

Options:
- `-u, --username`: Username
- `-p, --password`: Password
- `--password-stdin`: Take the password from stdin

**Log out from a registry**:
```
nerdctl logout [SERVER]
```

### Registry Certificates

For private registries with custom certificates, place certificates in:
- Rootful: `/etc/containerd/certs.d/<HOST:PORT>/` or `/etc/docker/certs.d/<HOST:PORT>/`
- Rootless: `~/.config/containerd/certs.d/<HOST:PORT>/` or `~/.config/docker/certs.d/<HOST:PORT>/`

## Advanced Features

### GPU Support

nerdctl supports NVIDIA GPU passthrough to containers:

```
nerdctl run -it --rm --gpus all nvidia/cuda:12.3.1-base-ubuntu20.04 nvidia-smi
```

Options for `--gpus`:
- `all`: Use all available GPUs
- Custom configuration: `--gpus '"capabilities=utility,compute",device=GPU-UUID'`

### BuildKit Integration

BuildKit provides advanced image building capabilities:

1. Set up BuildKit (different for rootful and rootless):
   ```
   # Rootless with containerd worker
   CONTAINERD_NAMESPACE=default containerd-rootless-setuptool.sh install-buildkit-containerd
   ```

2. Use advanced build features:
   ```
   nerdctl build --output=type=local,dest=./output --platform=linux/amd64,linux/arm64 .
   ```

### Namespace Management

**Create a namespace**:
```
nerdctl namespace create NAMESPACE
```

**List namespaces**:
```
nerdctl namespace ls
```

**Remove a namespace**:
```
nerdctl namespace remove NAMESPACE
```

### Security Features

nerdctl supports various security features:

- `--security-opt seccomp=profile.json`: Apply a seccomp profile
- `--security-opt apparmor=profile`: Apply an AppArmor profile
- `--cap-add`/`--cap-drop`: Add or drop Linux capabilities
- `--privileged`: Give extended privileges to the container

## Typical Workflow Example

```bash
# Create a container from an existing image
container=$(nerdctl run -d --name my-nginx nginx:latest)

# Execute a command in the container
nerdctl exec $container apt-get update
nerdctl exec $container apt-get install -y curl

# Copy local configuration files to the container
nerdctl cp ./nginx.conf $container:/etc/nginx/nginx.conf

# Commit the container to create a new image
nerdctl commit $container my-custom-nginx:latest

# Stop and remove the container
nerdctl stop $container
nerdctl rm $container

# Create a new container from our custom image
nerdctl run -d --name nginx-custom -p 8080:80 my-custom-nginx:latest

# Build an image using a Dockerfile
nerdctl build -t my-app:latest .

# Push the image to a registry
nerdctl push my-custom-nginx:latest docker.io/username/my-custom-nginx:latest

# List images
nerdctl images

# List containers
nerdctl ps -a
```