// File: /root/code/git.threefold.info/herocode/sal/src/virt/nerdctl/container_functions.rs

use crate::process::CommandResult;
use crate::virt::nerdctl::{execute_nerdctl_command, NerdctlError};

/// Run a container from an image
///
/// # Arguments
///
/// * `image` - Image to run
/// * `name` - Optional name for the container
/// * `detach` - Whether to run in detached mode
/// * `ports` - Optional port mappings
/// * `snapshotter` - Optional snapshotter to use
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn run(
    image: &str,
    name: Option<&str>,
    detach: bool,
    ports: Option<&[&str]>,
    snapshotter: Option<&str>,
) -> Result<CommandResult, NerdctlError> {
    let mut args = vec!["run"];
    
    if detach {
        args.push("-d");
    }
    
    if let Some(name_value) = name {
        args.push("--name");
        args.push(name_value);
    }
    
    if let Some(ports_value) = ports {
        for port in ports_value {
            args.push("-p");
            args.push(port);
        }
    }
    
    if let Some(snapshotter_value) = snapshotter {
        args.push("--snapshotter");
        args.push(snapshotter_value);
    }
    
    // Add flags to avoid BPF issues
    args.push("--cgroup-manager=cgroupfs");
    
    args.push(image);
    
    execute_nerdctl_command(&args)
}

/// Execute a command in a container
///
/// # Arguments
///
/// * `container` - Container name or ID
/// * `command` - Command to execute
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn exec(container: &str, command: &str) -> Result<CommandResult, NerdctlError> {
    execute_nerdctl_command(&["exec", container, "sh", "-c", command])
}

/// Copy files between container and local filesystem
///
/// # Arguments
///
/// * `source` - Source path (can be container:path or local path)
/// * `dest` - Destination path (can be container:path or local path)
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn copy(source: &str, dest: &str) -> Result<CommandResult, NerdctlError> {
    execute_nerdctl_command(&["cp", source, dest])
}

/// Stop a container
///
/// # Arguments
///
/// * `container` - Container name or ID
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn stop(container: &str) -> Result<CommandResult, NerdctlError> {
    execute_nerdctl_command(&["stop", container])
}

/// Remove a container
///
/// # Arguments
///
/// * `container` - Container name or ID
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn remove(container: &str) -> Result<CommandResult, NerdctlError> {
    execute_nerdctl_command(&["rm", container])
}

/// List containers
///
/// # Arguments
///
/// * `all` - Whether to list all containers (including stopped ones)
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn list(all: bool) -> Result<CommandResult, NerdctlError> {
    let mut args = vec!["ps"];
    
    if all {
        args.push("-a");
    }
    
    execute_nerdctl_command(&args)
}

/// Get container logs
///
/// # Arguments
///
/// * `container` - Container name or ID
///
/// # Returns
///
/// * `Result<CommandResult, NerdctlError>` - Command result or error
pub fn logs(container: &str) -> Result<CommandResult, NerdctlError> {
    execute_nerdctl_command(&["logs", container])
}