mod images;
mod cmd;
mod container_types;
mod container;
mod container_builder;
mod health_check;
mod health_check_script;
mod container_operations;
mod container_functions;
#[cfg(test)]
mod container_test;

use std::fmt;
use std::error::Error;
use std::io;

/// Error type for nerdctl operations
#[derive(Debug)]
pub enum NerdctlError {
    /// The nerdctl command failed to execute
    CommandExecutionFailed(io::Error),
    /// The nerdctl command executed but returned an error
    CommandFailed(String),
    /// Failed to parse JSON output
    JsonParseError(String),
    /// Failed to convert data
    ConversionError(String),
    /// Generic error
    Other(String),
}

impl fmt::Display for NerdctlError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NerdctlError::CommandExecutionFailed(e) => write!(f, "Failed to execute nerdctl command: {}", e),
            NerdctlError::CommandFailed(e) => write!(f, "Nerdctl command failed: {}", e),
            NerdctlError::JsonParseError(e) => write!(f, "Failed to parse JSON: {}", e),
            NerdctlError::ConversionError(e) => write!(f, "Conversion error: {}", e),
            NerdctlError::Other(e) => write!(f, "{}", e),
        }
    }
}

impl Error for NerdctlError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        match self {
            NerdctlError::CommandExecutionFailed(e) => Some(e),
            _ => None,
        }
    }
}

pub use images::*;
pub use cmd::*;
pub use container_types::{Container, HealthCheck, ContainerStatus, ResourceUsage};
pub use container_functions::*;
pub use health_check_script::*;