use super::{
    error::RfsError,
    cmd::execute_rfs_command,
    types::StoreSpec,
    builder::PackBuilder,
};

/// Pack a directory into a filesystem layer
///
/// # Arguments
///
/// * `directory` - Directory to pack
/// * `output` - Output file
/// * `store_specs` - Store specifications
///
/// # Returns
///
/// * `Result<(), RfsError>` - Success or error
pub fn pack_directory(directory: &str, output: &str, store_specs: &[StoreSpec]) -> Result<(), RfsError> {
    // Create a new pack builder
    let mut builder = PackBuilder::new(directory, output);
    
    // Add store specs
    for spec in store_specs {
        builder = builder.with_store_spec(spec.clone());
    }
    
    // Pack the directory
    builder.pack()
}

/// Unpack a filesystem layer
///
/// # Arguments
///
/// * `input` - Input file
/// * `directory` - Directory to unpack to
///
/// # Returns
///
/// * `Result<(), RfsError>` - Success or error
pub fn unpack(input: &str, directory: &str) -> Result<(), RfsError> {
    // Execute the unpack command
    let result = execute_rfs_command(&["unpack", "-m", input, directory])?;
    
    // Check for errors
    if !result.success {
        return Err(RfsError::Other(format!("Failed to unpack {}: {}", input, result.stderr)));
    }
    
    Ok(())
}

/// List the contents of a filesystem layer
///
/// # Arguments
///
/// * `input` - Input file
///
/// # Returns
///
/// * `Result<String, RfsError>` - File listing or error
pub fn list_contents(input: &str) -> Result<String, RfsError> {
    // Execute the list command
    let result = execute_rfs_command(&["list", "-m", input])?;
    
    // Check for errors
    if !result.success {
        return Err(RfsError::Other(format!("Failed to list contents of {}: {}", input, result.stderr)));
    }
    
    Ok(result.stdout)
}

/// Verify a filesystem layer
///
/// # Arguments
///
/// * `input` - Input file
///
/// # Returns
///
/// * `Result<bool, RfsError>` - Whether the layer is valid or error
pub fn verify(input: &str) -> Result<bool, RfsError> {
    // Execute the verify command
    let result = execute_rfs_command(&["verify", "-m", input])?;
    
    // Check for errors
    if !result.success {
        // If the command failed but returned a specific error about verification,
        // return false instead of an error
        if result.stderr.contains("verification failed") {
            return Ok(false);
        }
        
        return Err(RfsError::Other(format!("Failed to verify {}: {}", input, result.stderr)));
    }
    
    Ok(true)
}