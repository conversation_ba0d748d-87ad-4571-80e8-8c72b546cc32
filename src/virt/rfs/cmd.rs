use super::error::RfsError;
use crate::process::{run_command, CommandResult};
use std::cell::RefCell;
use std::thread_local;

// Thread-local storage for debug flag
thread_local! {
    static DEBUG: RefCell<bool> = RefCell::new(false);
}

/// Set the thread-local debug flag
#[allow(dead_code)]
pub fn set_thread_local_debug(debug: bool) {
    DEBUG.with(|d| {
        *d.borrow_mut() = debug;
    });
}

/// Get the current thread-local debug flag
pub fn thread_local_debug() -> bool {
    DEBUG.with(|d| *d.borrow())
}

/// Execute an RFS command with the given arguments
///
/// # Arguments
///
/// * `args` - Command arguments
///
/// # Returns
///
/// * `Result<CommandResult, RfsError>` - Command result or error
pub fn execute_rfs_command(args: &[&str]) -> Result<CommandResult, RfsError> {
    let debug = thread_local_debug();

    // Construct the command string
    let mut cmd = String::from("rfs");
    for arg in args {
        cmd.push(' ');
        cmd.push_str(arg);
    }

    if debug {
        println!("Executing RFS command: {}", cmd);
    }

    // Execute the command
    let result = run_command(&cmd)
        .map_err(|e| RfsError::CommandFailed(format!("Failed to execute RFS command: {}", e)))?;

    if debug {
        println!("RFS command result: {:?}", result);
    }

    // Check if the command was successful
    if !result.success && !result.stderr.is_empty() {
        return Err(RfsError::CommandFailed(result.stderr));
    }

    Ok(result)
}
