use std::collections::HashMap;

/// Represents a mounted filesystem
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct Mount {
    /// Mount ID
    pub id: String,
    /// Source path or URL
    pub source: String,
    /// Target mount point
    pub target: String,
    /// Filesystem type
    pub fs_type: String,
    /// Mount options
    pub options: Vec<String>,
}

/// Types of mounts supported by RFS
#[derive(Debug, Clone)]
pub enum MountType {
    /// Local filesystem
    Local,
    /// SSH remote filesystem
    SSH,
    /// S3 object storage
    S3,
    /// WebDAV remote filesystem
    WebDAV,
    /// Custom mount type
    Custom(String),
}

impl MountType {
    /// Convert mount type to string representation
    pub fn to_string(&self) -> String {
        match self {
            MountType::Local => "local".to_string(),
            MountType::SSH => "ssh".to_string(),
            MountType::S3 => "s3".to_string(),
            MountType::WebDAV => "webdav".to_string(),
            MountType::Custom(s) => s.clone(),
        }
    }
    
    /// Create a MountType from a string
    pub fn from_string(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "local" => MountType::Local,
            "ssh" => MountType::SSH,
            "s3" => MountType::S3,
            "webdav" => MountType::WebDAV,
            _ => MountType::Custom(s.to_string()),
        }
    }
}

/// Store specification for packing operations
#[derive(Debug, Clone)]
pub struct StoreSpec {
    /// Store type (e.g., "file", "s3")
    pub spec_type: String,
    /// Store options
    pub options: HashMap<String, String>,
}

impl StoreSpec {
    /// Create a new store specification
    ///
    /// # Arguments
    ///
    /// * `spec_type` - Store type (e.g., "file", "s3")
    ///
    /// # Returns
    ///
    /// * `Self` - New store specification
    pub fn new(spec_type: &str) -> Self {
        Self {
            spec_type: spec_type.to_string(),
            options: HashMap::new(),
        }
    }

    /// Add an option to the store specification
    ///
    /// # Arguments
    ///
    /// * `key` - Option key
    /// * `value` - Option value
    ///
    /// # Returns
    ///
    /// * `Self` - Updated store specification for method chaining
    pub fn with_option(mut self, key: &str, value: &str) -> Self {
        self.options.insert(key.to_string(), value.to_string());
        self
    }

    /// Convert the store specification to a string
    ///
    /// # Returns
    ///
    /// * `String` - String representation of the store specification
    pub fn to_string(&self) -> String {
        let mut result = self.spec_type.clone();
        
        if !self.options.is_empty() {
            result.push_str(":");
            let options: Vec<String> = self.options
                .iter()
                .map(|(k, v)| format!("{}={}", k, v))
                .collect();
            result.push_str(&options.join(","));
        }
        
        result
    }
}