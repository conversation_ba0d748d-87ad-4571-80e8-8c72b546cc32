use std::fmt;
use std::error::Error;

/// Error types for RFS operations
#[derive(Debug)]
pub enum RfsError {
    /// Command execution failed
    CommandFailed(String),
    /// Invalid argument provided
    InvalidArgument(String),
    /// Mount operation failed
    MountFailed(String),
    /// Unmount operation failed
    UnmountFailed(String),
    /// List operation failed
    ListFailed(String),
    /// Pack operation failed
    PackFailed(String),
    /// Other error
    Other(String),
}

impl fmt::Display for RfsError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RfsError::CommandFailed(msg) => write!(f, "RFS command failed: {}", msg),
            RfsError::InvalidArgument(msg) => write!(f, "Invalid argument: {}", msg),
            RfsError::MountFailed(msg) => write!(f, "Mount failed: {}", msg),
            RfsError::UnmountFailed(msg) => write!(f, "Unmount failed: {}", msg),
            RfsError::ListFailed(msg) => write!(f, "List failed: {}", msg),
            RfsError::PackFailed(msg) => write!(f, "Pack failed: {}", msg),
            RfsError::Other(msg) => write!(f, "Other error: {}", msg),
        }
    }
}

impl Error for RfsError {}

impl From<std::io::Error> for RfsError {
    fn from(error: std::io::Error) -> Self {
        RfsError::Other(format!("IO error: {}", error))
    }
}