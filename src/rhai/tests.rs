//! Tests for Rhai integration
//!
//! This module contains tests for the Rhai integration.

#[cfg(test)]
mod tests {
    use super::super::register;
    use rhai::Engine;
    use std::fs;
    use std::path::Path;

    #[test]
    fn test_register() {
        let mut engine = Engine::new();
        assert!(register(&mut engine).is_ok());
    }

    // OS Module Tests

    #[test]
    fn test_exist_function() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test with a file that definitely exists
        let result = engine.eval::<bool>(r#"exist("Cargo.toml")"#).unwrap();
        assert!(result);

        // Test with a file that definitely doesn't exist
        let result = engine
            .eval::<bool>(r#"exist("non_existent_file.xyz")"#)
            .unwrap();
        assert!(!result);
    }

    #[test]
    fn test_mkdir_and_delete() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        let test_dir = "test_rhai_dir";

        // Clean up from previous test runs if necessary
        if Path::new(test_dir).exists() {
            fs::remove_dir_all(test_dir).unwrap();
        }

        // Create directory using Rhai
        let script = format!(r#"mkdir("{}")"#, test_dir);
        let result = engine.eval::<String>(&script).unwrap();
        assert!(result.contains("Successfully created directory"));
        assert!(Path::new(test_dir).exists());

        // Delete directory using Rhai
        let script = format!(r#"delete("{}")"#, test_dir);
        let result = engine.eval::<String>(&script).unwrap();
        assert!(result.contains("Successfully deleted directory"));
        assert!(!Path::new(test_dir).exists());
    }

    #[test]
    fn test_file_size() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Create a test file
        let test_file = "test_rhai_file.txt";
        let test_content = "Hello, Rhai!";
        fs::write(test_file, test_content).unwrap();

        // Get file size using Rhai
        let script = format!(r#"file_size("{}")"#, test_file);
        let size = engine.eval::<i64>(&script).unwrap();
        assert_eq!(size, test_content.len() as i64);

        // Clean up
        fs::remove_file(test_file).unwrap();
    }

    #[test]
    fn test_error_handling() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Try to get the size of a non-existent file
        let result = engine.eval::<i64>(r#"file_size("non_existent_file.xyz")"#);
        assert!(result.is_err());

        let err = result.unwrap_err();
        let err_str = err.to_string();
        println!("Error string: {}", err_str);
        // The actual error message is "No files found matching..."
        assert!(
            err_str.contains("No files found matching")
                || err_str.contains("File not found")
                || err_str.contains("File system error")
        );
    }

    // Process Module Tests

    #[test]
    fn test_which_function() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test with a command that definitely exists (like "ls" on Unix or "cmd" on Windows)
        #[cfg(target_os = "windows")]
        let cmd = "cmd";

        #[cfg(any(target_os = "macos", target_os = "linux"))]
        let cmd = "ls";

        let script = format!(r#"which("{}")"#, cmd);
        let result = engine.eval::<String>(&script).unwrap();
        assert!(!result.is_empty());

        // Test with a command that definitely doesn't exist
        let script = r#"which("non_existent_command_xyz123")"#;
        let result = engine.eval::<()>(&script).unwrap();
        assert_eq!(result, ());
    }

    #[test]
    fn test_run_with_options() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test running a command with custom options
        #[cfg(target_os = "windows")]
        let script = r#"
            let options = new_run_options();
            options["die"] = true;
            options["silent"] = false;
            options["log"] = true;
            let result = run("echo Hello World", options);
            result.success && result.stdout.contains("Hello World")
        "#;

        #[cfg(any(target_os = "macos", target_os = "linux"))]
        let script = r#"
            let options = new_run_options();
            options["die"] = true;
            options["silent"] = false;
            options["log"] = true;
            let result = run("echo 'Hello World'", options);
            result.success && result.stdout.contains("Hello World")
        "#;

        let result = engine.eval::<bool>(script).unwrap();
        assert!(result);
    }

    #[test]
    fn test_run_command() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test a simple echo command
        #[cfg(target_os = "windows")]
        let script = r#"
            let result = run_command("echo Hello World");
            result.success && result.stdout.contains("Hello World")
        "#;

        #[cfg(any(target_os = "macos", target_os = "linux"))]
        let script = r#"
            let result = run_command("echo 'Hello World'");
            result.success && result.stdout.contains("Hello World")
        "#;

        let result = engine.eval::<bool>(script).unwrap();
        assert!(result);
    }

    #[test]
    fn test_run_silent() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test a simple echo command with silent execution
        #[cfg(target_os = "windows")]
        let script = r#"
            let result = run_silent("echo Hello World");
            result.success && result.stdout.contains("Hello World")
        "#;

        #[cfg(any(target_os = "macos", target_os = "linux"))]
        let script = r#"
            let result = run_silent("echo 'Hello World'");
            result.success && result.stdout.contains("Hello World")
        "#;

        let result = engine.eval::<bool>(script).unwrap();
        assert!(result);
    }

    #[test]
    fn test_process_list() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test listing processes (should return a non-empty array)
        let script = r#"
            let processes = process_list("");
            processes.len() > 0
        "#;

        let result = engine.eval::<bool>(script).unwrap();
        assert!(result);
    }

    // Git Module Tests

    #[test]
    fn test_git_module_registration() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test that git functions are registered by trying to use them
        let script = r#"
            // Try to use git_clone function
            let result = true;

            try {
                // This should fail but not crash
                git_clone("test-url");
            } catch(err) {
                // Expected error
                result = err.contains("Git error");
            }

            result
        "#;

        let result = engine.eval::<bool>(script).unwrap();
        assert!(result);
    }

    #[test]
    fn test_git_parse_url() {
        let mut engine = Engine::new();
        register(&mut engine).unwrap();

        // Test parsing a git URL
        let script = r#"
            // We can't directly test git_clone without actually cloning,
            // but we can test that the function exists and doesn't error
            // when called with invalid parameters

            let result = false;

            try {
                // This should fail but not crash
                git_clone("invalid-url");
            } catch(err) {
                // Expected error
                result = err.contains("Git error");
            }

            result
        "#;

        let result = engine.eval::<bool>(script).unwrap();
        assert!(result);
    }
}
