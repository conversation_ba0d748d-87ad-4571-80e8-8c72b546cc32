//! Error handling for Rhai integration
//!
//! This module provides utilities for converting SAL error types to Rhai error types.

use rhai::{EvalAltResult, Position};
use crate::os::{FsError, DownloadError};
use crate::os::package::PackageError;

/// Convert a FsError to a Rhai EvalAltResult
pub fn fs_error_to_rhai_error(err: FsError) -> Box<EvalAltResult> {
    let err_msg = err.to_string();
    Box::new(EvalAltResult::ErrorRuntime(
        err_msg.into(),
        Position::NONE
    ))
}

/// Convert a DownloadError to a Rhai EvalAltResult
pub fn download_error_to_rhai_error(err: DownloadError) -> Box<EvalAltResult> {
    let err_msg = err.to_string();
    Box::new(EvalAltResult::ErrorRuntime(
        err_msg.into(),
        Position::NONE
    ))
}

/// Convert a PackageError to a Rhai EvalAltResult
pub fn package_error_to_rhai_error(err: PackageError) -> Box<EvalAltResult> {
    let err_msg = err.to_string();
    Box::new(EvalAltResult::ErrorRuntime(
        err_msg.into(),
        Position::NONE
    ))
}

/// Register error types with the Rhai engine
pub fn register_error_types(engine: &mut rhai::Engine) -> Result<(), Box<EvalAltResult>> {
    // Register helper functions for error handling
    // Note: We don't register the error types directly since they don't implement Clone
    // Instead, we'll convert them to strings in the wrappers
    
    // Register functions to get error messages
    engine.register_fn("fs_error_message", |err_msg: &str| -> String {
        format!("File system error: {}", err_msg)
    });
    
    engine.register_fn("download_error_message", |err_msg: &str| -> String {
        format!("Download error: {}", err_msg)
    });
    
    engine.register_fn("package_error_message", |err_msg: &str| -> String {
        format!("Package management error: {}", err_msg)
    });
    
    Ok(())
}

/// Trait for converting SAL errors to Rhai errors
pub trait ToRhaiError<T> {
    /// Convert the error to a Rhai EvalAltResult
    fn to_rhai_error(self) -> Result<T, Box<EvalAltResult>>;
}

impl<T> ToRhaiError<T> for Result<T, FsError> {
    fn to_rhai_error(self) -> Result<T, Box<EvalAltResult>> {
        self.map_err(fs_error_to_rhai_error)
    }
}

impl<T> ToRhaiError<T> for Result<T, DownloadError> {
    fn to_rhai_error(self) -> Result<T, Box<EvalAltResult>> {
        self.map_err(download_error_to_rhai_error)
    }
}

impl<T> ToRhaiError<T> for Result<T, PackageError> {
    fn to_rhai_error(self) -> Result<T, Box<EvalAltResult>> {
        self.map_err(package_error_to_rhai_error)
    }
}