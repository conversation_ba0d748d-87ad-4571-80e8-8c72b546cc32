//! Rhai scripting integration for the SAL library
//!
//! This module provides integration with the Rhai scripting language,
//! allowing SAL functions to be called from Rhai scripts.

mod buildah;
mod error;
mod git;
mod nerdctl;
mod os;
mod postgresclient;
mod process;
mod redisclient;
mod rfs;
mod vault;
mod text;
mod zinit;
mod mycelium;

#[cfg(test)]
mod tests;

// Re-export common Rhai types for convenience
pub use rhai::{Array, Dynamic, Engine, EvalAltResult, Map};

// Re-export error module
pub use error::*;

// Re-export specific functions from modules to avoid name conflicts
pub use os::{
    delete,
    // Download functions
    download,
    download_install,
    // File system functions
    exist,
    file_size,
    find_dir,
    find_dirs,
    find_file,
    find_files,
    mkdir,
    register_os_module,
    rsync,
};

// Re-export Redis client module registration function
pub use redisclient::register_redisclient_module;

// Re-export PostgreSQL client module registration function
pub use postgresclient::register_postgresclient_module;

pub use process::{
    kill,
    process_get,
    process_list,
    register_process_module,
    // Run functions
    // Process management functions
    which,
};

// Re-export buildah functions
pub use buildah::bah_new;
pub use buildah::register_bah_module;

// Re-export nerdctl functions
pub use nerdctl::register_nerdctl_module;
pub use nerdctl::{
    nerdctl_copy,
    nerdctl_exec,
    nerdctl_image_build,
    nerdctl_image_commit,
    nerdctl_image_pull,
    nerdctl_image_push,
    nerdctl_image_remove,
    nerdctl_image_tag,
    // Image functions
    nerdctl_images,
    nerdctl_list,
    nerdctl_remove,
    // Container functions
    nerdctl_run,
    nerdctl_run_with_name,
    nerdctl_run_with_port,
    nerdctl_stop,
};

// Re-export RFS module
pub use rfs::register as register_rfs_module;

// Re-export git module
pub use crate::git::{GitRepo, GitTree};
pub use git::register_git_module;

// Re-export zinit module
pub use zinit::register_zinit_module;

// Re-export mycelium module
pub use mycelium::register_mycelium_module;

// Re-export text module
pub use text::register_text_module;
// Re-export text functions directly from text module
pub use crate::text::{
    // Dedent functions
    dedent,
    // Fix functions
    name_fix,
    path_fix,
    prefix,
};

// Re-export TextReplacer functions
pub use text::*;

// Re-export crypto module
pub use vault::register_crypto_module;

// Rename copy functions to avoid conflicts
pub use os::copy as os_copy;

/// Register all SAL modules with the Rhai engine
///
/// # Arguments
///
/// * `engine` - The Rhai engine to register the modules with
///
/// # Example
///
/// ```ignore
/// use rhai::Engine;
/// use sal::rhai;
///
/// let mut engine = Engine::new();
/// rhai::register(&mut engine);
///
/// // Now you can use SAL functions in Rhai scripts
/// // You can evaluate Rhai scripts with SAL functions
/// let result = engine.eval::<i64>("exist('some_file.txt')").unwrap();
/// ```
pub fn register(engine: &mut Engine) -> Result<(), Box<rhai::EvalAltResult>> {
    // Register OS module functions
    os::register_os_module(engine)?;

    // Register Process module functions
    process::register_process_module(engine)?;

    // Register Buildah module functions
    buildah::register_bah_module(engine)?;

    // Register Nerdctl module functions
    nerdctl::register_nerdctl_module(engine)?;

    // Register Git module functions
    git::register_git_module(engine)?;

    
    // Register Zinit module functions
    zinit::register_zinit_module(engine)?;
    
    // Register Mycelium module functions
    mycelium::register_mycelium_module(engine)?;
    
    // Register Text module functions
    text::register_text_module(engine)?;

    // Register RFS module functions
    rfs::register(engine)?;
    
    // Register Crypto module functions
    vault::register_crypto_module(engine)?;
    

    // Register Redis client module functions
    redisclient::register_redisclient_module(engine)?;

    // Register PostgreSQL client module functions
    postgresclient::register_postgresclient_module(engine)?;

    // Register utility functions
    engine.register_fn("is_def_fn", |_name: &str| -> bool {
        // This is a utility function to check if a function is defined in the engine
        // For testing purposes, we'll just return true
        true
    });

    // Future modules can be registered here

    Ok(())
}
