//! Herodo binary entry point
//!
//! This is the main entry point for the herodo binary.
//! It parses command line arguments and calls into the implementation in the cmd module.

use clap::{App, Arg};
use env_logger;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize the logger
    env_logger::init();
    
    // Parse command line arguments
    let matches = App::new("herodo")
        .version("0.1.0")
        .author("SAL Team")
        .about("Executes Rhai scripts for SAL")
        .arg(
            Arg::with_name("path")
                .short("p")
                .long("path")
                .value_name("PATH")
                .help("Path to a Rhai script file or directory containing Rhai scripts")
                .required(true)
                .takes_value(true),
        )
        .get_matches();

    // Get the script path from arguments
    let script_path = matches.value_of("path").unwrap();
    
    // Call the run function from the cmd module
    sal::cmd::herodo::run(script_path)
}
