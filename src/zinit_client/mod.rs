use lazy_static::lazy_static;
use serde_json::{Map, Value};
use std::collections::HashMap;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::{Arc, Mutex, Once};
use zinit_client::{ServiceState, ServiceStatus as Status, ZinitClient, ZinitError as ClientError};

// Global Zinit client instance using lazy_static
lazy_static! {
    static ref ZINIT_CLIENT: Mutex<Option<Arc<ZinitClientWrapper>>> = Mutex::new(None);
    static ref INIT: Once = Once::new();
}

// Wrapper for Zinit client to handle connection
pub struct ZinitClientWrapper {
    client: ZinitClient,
    initialized: AtomicBool,
}

impl ZinitClientWrapper {
    // Create a new Zinit client wrapper
    fn new(client: ZinitClient) -> Self {
        ZinitClientWrapper {
            client,
            initialized: AtomicBool::new(false),
        }
    }

    // Initialize the client
    async fn initialize(&self) -> Result<(), ClientError> {
        if self.initialized.load(Ordering::Relaxed) {
            return Ok(());
        }

        // Try to list services to check if the connection works
        let _ = self.client.list().await.map_err(|e| {
            eprintln!("Failed to initialize Zinit client: {}", e);
            e
        })?;

        self.initialized.store(true, Ordering::Relaxed);
        Ok(())
    }

    // List all services
    pub async fn list(&self) -> Result<HashMap<String, ServiceState>, ClientError> {
        self.client.list().await
    }

    // Get status of a service
    pub async fn status(&self, name: &str) -> Result<Status, ClientError> {
        self.client.status(name).await
    }

    // Start a service
    pub async fn start(&self, name: &str) -> Result<(), ClientError> {
        self.client.start(name).await
    }

    // Stop a service
    pub async fn stop(&self, name: &str) -> Result<(), ClientError> {
        self.client.stop(name).await
    }

    // Restart a service
    pub async fn restart(&self, name: &str) -> Result<(), ClientError> {
        self.client.restart(name).await
    }

    // Monitor a service
    pub async fn monitor(&self, name: &str) -> Result<(), ClientError> {
        self.client.monitor(name).await
    }

    // Forget a service
    pub async fn forget(&self, name: &str) -> Result<(), ClientError> {
        self.client.forget(name).await
    }

    // Send a signal to a service
    pub async fn kill(&self, name: &str, signal: &str) -> Result<(), ClientError> {
        self.client.kill(name, signal).await
    }

    // Create a new service
    pub async fn create_service(
        &self,
        name: &str,
        content: Map<String, Value>,
    ) -> Result<String, ClientError> {
        self.client.create_service(name, content).await
    }

    // Delete a service
    pub async fn delete_service(&self, name: &str) -> Result<String, ClientError> {
        self.client.delete_service(name).await
    }

    // Get a service configuration
    pub async fn get_service(&self, name: &str) -> Result<Value, ClientError> {
        self.client.get_service(name).await
    }

    // Shutdown the system
    pub async fn shutdown(&self) -> Result<(), ClientError> {
        self.client.shutdown().await
    }

    // Reboot the system
    pub async fn reboot(&self) -> Result<(), ClientError> {
        self.client.reboot().await
    }

    // Start HTTP server
    pub async fn start_http_server(&self, address: &str) -> Result<String, ClientError> {
        self.client.start_http_server(address).await
    }

    // Stop HTTP server
    pub async fn stop_http_server(&self) -> Result<(), ClientError> {
        self.client.stop_http_server().await
    }

    // Get logs
    pub async fn logs(&self, filter: Option<String>) -> Result<Vec<String>, ClientError> {
        self.client.logs(filter).await
    }
}

// Get the Zinit client instance
pub async fn get_zinit_client(socket_path: &str) -> Result<Arc<ZinitClientWrapper>, ClientError> {
    // Check if we already have a client
    {
        let guard = ZINIT_CLIENT.lock().unwrap();
        if let Some(ref client) = &*guard {
            return Ok(Arc::clone(client));
        }
    }

    // Create a new client
    let client = create_zinit_client(socket_path).await?;

    // Store the client globally
    {
        let mut guard = ZINIT_CLIENT.lock().unwrap();
        *guard = Some(Arc::clone(&client));
    }

    Ok(client)
}

// Create a new Zinit client
async fn create_zinit_client(socket_path: &str) -> Result<Arc<ZinitClientWrapper>, ClientError> {
    // Connect via Unix socket
    let client = ZinitClient::unix_socket(socket_path).await?;
    let wrapper = Arc::new(ZinitClientWrapper::new(client));

    // Initialize the client
    wrapper.initialize().await?;

    Ok(wrapper)
}

// Reset the Zinit client
pub async fn reset(socket_path: &str) -> Result<(), ClientError> {
    // Clear the existing client
    {
        let mut client_guard = ZINIT_CLIENT.lock().unwrap();
        *client_guard = None;
    }

    // Create a new client, only return error if it fails
    get_zinit_client(socket_path).await?;
    Ok(())
}

// Convenience functions for common operations

// List all services
pub async fn list(socket_path: &str) -> Result<HashMap<String, String>, ClientError> {
    let client = get_zinit_client(socket_path).await?;
    client.list().await
}

// Get status of a service
pub async fn status(socket_path: &str, name: &str) -> Result<Status, ClientError> {
    let client = get_zinit_client(socket_path).await?;
    client.status(name).await
}

// Start a service
pub async fn start(socket_path: &str, name: &str) -> Result<(), ClientError> {
    let client = get_zinit_client(socket_path).await?;
    client.start(name).await
}

// Stop a service
pub async fn stop(socket_path: &str, name: &str) -> Result<(), ClientError> {
    let client = get_zinit_client(socket_path).await?;
    client.stop(name).await
}

// Restart a service
pub async fn restart(socket_path: &str, name: &str) -> Result<(), ClientError> {
    let client = get_zinit_client(socket_path).await?;
    client.restart(name).await
}
