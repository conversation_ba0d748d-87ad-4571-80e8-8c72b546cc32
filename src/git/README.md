# SAL `git` Module

The `git` module in SAL provides comprehensive functionalities for interacting with Git repositories. It offers both high-level abstractions for common Git workflows and a flexible executor for running arbitrary Git commands with integrated authentication.

This module is central to SAL's capabilities for managing source code, enabling automation of development tasks, and integrating with version control systems.

## Core Components

The module is primarily composed of two main parts:

1.  **Repository and Tree Management (`git.rs`)**: Defines `GitTree` and `GitRepo` structs for a more structured, object-oriented approach to Git operations.
2.  **Command Execution with Authentication (`git_executor.rs`)**: Provides `GitExecutor` for running any Git command, with a focus on handling authentication via configurations stored in Redis.

### 1. Repository and Tree Management (`GitTree` & `GitRepo`)

These components allow for programmatic management of Git repositories.

*   **`GitTree`**: Represents a directory (base path) that can contain multiple Git repositories.
    *   `new(base_path)`: Creates a new `GitTree` instance for the given base path.
    *   `list()`: Lists all Git repositories found under the base path.
    *   `find(pattern)`: Finds repositories within the tree that match a given name pattern (supports wildcards).
    *   `get(path_or_url)`: Retrieves `GitRepo` instances. If a local path/pattern is given, it finds existing repositories. If a Git URL is provided, it will clone the repository into a structured path (`base_path/server/account/repo`) if it doesn't already exist.

*   **`GitRepo`**: Represents a single Git repository.
    *   `new(path)`: Creates a `GitRepo` instance for the repository at the given path.
    *   `path()`: Returns the local file system path to the repository.
    *   `has_changes()`: Checks if the repository has uncommitted local changes.
    *   `pull()`: Pulls the latest changes from the remote. Fails if local changes exist.
    *   `reset()`: Performs a hard reset (`git reset --hard HEAD`) and cleans untracked files (`git clean -fd`).
    *   `commit(message)`: Stages all changes (`git add .`) and commits them with the given message.
    *   `push()`: Pushes committed changes to the remote repository.

*   **`GitError`**: A comprehensive enum for errors related to `GitTree` and `GitRepo` operations (e.g., Git not installed, invalid URL, repository not found, local changes exist).

*   **`parse_git_url(url)`**: A utility function to parse HTTPS and SSH Git URLs into server, account, and repository name components.

### 2. Command Execution with Authentication (`GitExecutor`)

`GitExecutor` is designed for flexible execution of any Git command, with a special emphasis on handling authentication for remote operations.

*   **`GitExecutor::new()` / `GitExecutor::default()`**: Creates a new executor instance.
*   **`GitExecutor::init()`**: Initializes the executor by attempting to load authentication configurations from Redis (key: `herocontext:git`). If Redis is unavailable or the config is missing, it proceeds without specific auth configurations, relying on system defaults.
*   **`GitExecutor::execute(args: &[&str])`**: The primary method to run a Git command (e.g., `executor.execute(&["clone", "https://github.com/user/repo.git", "myrepo"])`).
    *   It intelligently attempts to apply authentication based on the command and the loaded configuration.

#### Authentication Configuration (`herocontext:git` in Redis)

The `GitExecutor` can load its authentication settings from a JSON object stored in Redis under the key `herocontext:git`. The structure is as follows:

```json
{
  "status": "ok", // or "error"
  "auth": {
    "github.com": {
      "sshagent": true // Use SSH agent for github.com
    },
    "gitlab.example.com": {
      "key": "/path/to/ssh/key_for_gitlab" // Use specific SSH key
    },
    "dev.azure.com": {
      "username": "your_username",
      "password": "your_pat_or_password" // Use HTTPS credentials
    }
    // ... other server configurations
  }
}
```

*   **Authentication Methods Supported**:
    *   **SSH Agent**: If `sshagent: true` is set for a server, and an SSH agent is loaded with identities.
    *   **SSH Key**: If `key: "/path/to/key"` is specified, `GIT_SSH_COMMAND` is used to point to this key.
    *   **Username/Password (HTTPS)**: If `username` and `password` are provided, HTTPS URLs are rewritten to include these credentials (e.g., `************************/repo.git`).

*   **`GitExecutorError`**: An enum for errors specific to `GitExecutor`, including command failures, Redis errors, JSON parsing issues, and authentication problems (e.g., `SshAgentNotLoaded`, `InvalidAuthConfig`).

## Usage with `herodo`

The `herodo` CLI tool likely leverages `GitExecutor` to provide its scriptable Git functionalities. This allows Rhai scripts executed by `herodo` to perform Git operations using the centrally managed authentication configurations from Redis, promoting secure and consistent access to Git repositories.

## Error Handling

Both `git.rs` and `git_executor.rs` define their own specific error enums (`GitError` and `GitExecutorError` respectively) to provide detailed information about issues encountered during Git operations. These errors cover a wide range of scenarios from command execution failures to authentication problems and invalid configurations.

## Summary

The `git` module offers a powerful and flexible interface to Git, catering to both simple, high-level repository interactions and complex, authenticated command execution scenarios. Its integration with Redis for authentication configuration makes it particularly well-suited for automated systems and tools like `herodo`.
