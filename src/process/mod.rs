//! # Process Module
//! 
//! The `process` module provides functionality for managing and interacting with
//! system processes across different platforms. It includes capabilities for:
//! 
//! - Running commands and scripts
//! - Listing and filtering processes
//! - Killing processes
//! - Checking for command existence
//! 
//! This module is designed to work consistently across Windows, macOS, and Linux.

mod run;
mod mgmt;
#[cfg(test)]
mod tests;

pub use run::*;
pub use mgmt::*;