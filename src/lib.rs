//! # System Abstraction Layer (SAL)
//!
//! `sal` is a library that provides a unified interface for interacting with
//! operating system features across different platforms. It abstracts away the
//! platform-specific details, allowing developers to write cross-platform code
//! with ease.
//!
//! ## Features
//!
//! - File system operations
//! - Process management
//! - System information
//! - Network operations
//! - Environment variables
//! - Cryptographic operations

use std::io;
use thiserror::Error;

/// Error types for the SAL library
#[derive(Error, Debug)]
pub enum Error {
    /// An error occurred during an I/O operation
    #[error("I/O error: {0}")]
    Io(#[from] io::Error),

    /// An error specific to the SAL library
    #[error("SAL error: {0}")]
    Sal(String),

    /// An error that occurred in a platform-specific operation
    #[error("Platform-specific error: {0}")]
    Platform(String),
}

/// Result type for SAL operations
pub type Result<T> = std::result::Result<T, Error>;

// Re-export modules
pub mod cmd;
pub mod git;
pub mod os;
pub mod postgresclient;
pub mod process;
pub mod redisclient;
pub mod rhai;
pub mod text;
pub mod virt;
pub mod vault;
pub mod zinit_client;
pub mod mycelium;

// Version information
/// Returns the version of the SAL library
pub fn version() -> &'static str {
    env!("CARGO_PKG_VERSION")
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!version().is_empty());
    }
}
