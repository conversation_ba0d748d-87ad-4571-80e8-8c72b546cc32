// 02_process_management.rhai
// Tests for process management functions in the Process module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

print("=== Testing Process Management Functions ===");

// Test process_list function
print("Testing process_list() function...");
let all_processes = process_list("");
assert_true(all_processes.len() > 0, "There should be at least one running process");
print(`✓ process_list(): Found ${all_processes.len()} processes`);

// Test process properties
print("Testing process properties...");
let first_process = all_processes[0];
assert_true(first_process.pid > 0, "Process PID should be a positive number");
assert_true(first_process.name.len() > 0, "Process name should not be empty");
print(`✓ Process properties: PID=${first_process.pid}, Name=${first_process.name}, CPU=${first_process.cpu}%, Memory=${first_process.memory}`);

// Test process_list with a pattern
print("Testing process_list() with a pattern...");
// Use a pattern that's likely to match at least one process on most systems
let pattern = "sh";
let matching_processes = process_list(pattern);
print(`Found ${matching_processes.len()} processes matching '${pattern}'`);
if (matching_processes.len() > 0) {
    let matched_process = matching_processes[0];
    print(`✓ process_list(pattern): Found process ${matched_process.name} with PID ${matched_process.pid}`);
} else {
    print(`Note: No processes found matching '${pattern}'. This is not necessarily an error.`);
}

// Test process_get function
// Note: We'll only test this if we found matching processes above
if (matching_processes.len() == 1) {
    print("Testing process_get() function...");
    let process = process_get(pattern);
    assert_true(process.pid > 0, "Process PID should be a positive number");
    assert_true(process.name.contains(pattern), "Process name should contain the pattern");
    print(`✓ process_get(): Found process ${process.name} with PID ${process.pid}`);
} else {
    print("Skipping process_get() test as it requires exactly one matching process");
}

// Note: We won't test the kill function as it could disrupt the system

print("All process management tests completed successfully!");
