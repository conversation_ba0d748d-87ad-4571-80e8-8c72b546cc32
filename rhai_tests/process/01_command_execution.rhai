// 01_command_execution.rhai
// Tests for command execution in the Process module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

print("=== Testing Basic Command Execution ===");

// Test running a simple command
print("Testing run() with a simple command...");
let result = run("echo Hello, World!").execute();
assert_true(result.success, "Command should succeed");
assert_true(result.stdout.contains("Hello, World!"), "Command output should contain the expected text");
print(`✓ run().execute(): Command executed successfully`);

// Test running a command with arguments
print("Testing run() with command arguments...");
let result_with_args = run("echo Hello from Rhai tests").execute();
assert_true(result_with_args.success, "Command with arguments should succeed");
assert_true(result_with_args.stdout.contains("Hello from Rhai tests"), "Command output should contain the expected text");
print(`✓ run().execute(): Command with arguments executed successfully`);

// Test running a command with environment variables
print("Testing run() with environment variables...");
let env_result = run("echo $HOME").execute();
assert_true(env_result.success, "Command with environment variables should succeed");
assert_true(env_result.stdout.trim() != "", "Environment variable should be expanded");
print(`✓ run().execute(): Command with environment variables executed successfully`);

// Test running a multiline script
print("Testing run() with a multiline script...");
let script_result = run(`
    echo "Line 1"
    echo "Line 2"
    echo "Line 3"
`).execute();
assert_true(script_result.success, "Multiline script should succeed");
assert_true(script_result.stdout.contains("Line 1") && script_result.stdout.contains("Line 2") && script_result.stdout.contains("Line 3"),
           "Script output should contain all lines");
print(`✓ run().execute(): Multiline script executed successfully`);

// Test which function
print("Testing which() function...");
let bash_path = which("bash");
assert_true(bash_path != "", "bash should be found in PATH");
print(`✓ which(): Found bash at ${bash_path}`);

// Test a command that doesn't exist
let nonexistent_cmd = which("this_command_does_not_exist_12345");
if nonexistent_cmd == "" {
    print(`✓ which(): Correctly reported that nonexistent command was not found`);
} else {
    print(`Note: Unexpectedly found command at ${nonexistent_cmd}`);
}

print("All command execution tests completed successfully!");
