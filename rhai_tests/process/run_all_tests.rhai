// run_all_tests.rhai
// Runs all Process module tests

print("=== Running Process Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Run each test directly
let passed = 0;
let failed = 0;

// Test 1: Command Execution
print("\n--- Running Command Execution Tests ---");
try {
    // Test running a simple command
    print("Testing run() with a simple command...");
    let result = run("echo Hello, World!").execute();
    assert_true(result.success, "Command should succeed");
    assert_true(result.stdout.contains("Hello, World!"), "Command output should contain the expected text");
    print(`✓ run().execute(): Command executed successfully`);

    // Test which function
    print("Testing which() function...");
    let bash_path = which("bash");
    assert_true(bash_path != "", "bash should be found in PATH");
    print(`✓ which(): Found bash at ${bash_path}`);

    print("--- Command Execution Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Command Execution Tests: ${err}`);
    failed += 1;
}

// Test 2: Process Management
print("\n--- Running Process Management Tests ---");
try {
    // Test process_list function
    print("Testing process_list() function...");
    let all_processes = process_list("");
    assert_true(all_processes.len() > 0, "There should be at least one running process");
    print(`✓ process_list(): Found ${all_processes.len()} processes`);

    // Test process properties
    print("Testing process properties...");
    let first_process = all_processes[0];
    assert_true(first_process.pid > 0, "Process PID should be a positive number");
    assert_true(first_process.name.len() > 0, "Process name should not be empty");
    print(`✓ Process properties: PID=${first_process.pid}, Name=${first_process.name}`);

    print("--- Process Management Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Process Management Tests: ${err}`);
    failed += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Total: ${passed + failed}`);

if failed == 0 {
    print("\n✅ All tests passed!");
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
