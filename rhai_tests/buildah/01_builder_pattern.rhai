// 01_builder_pattern.rhai
// Tests for Buildah Builder pattern

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

// Helper function to check if buildah is available
fn is_buildah_available() {
    try {
        let result = run("which buildah");
        return result.success;
    } catch(err) {
        return false;
    }
}

print("=== Testing Buildah Builder Pattern ===");

// Check if buildah is available
let buildah_available = is_buildah_available();
if !buildah_available {
    print("Buildah is not available. Skipping Buildah tests.");
    // Exit gracefully without error
    return;
}

print("✓ Buildah is available");

// Test creating a new Builder
print("Testing bah_new()...");
try {
    let builder = bah_new("rhai_test_container", "alpine:latest");
    
    // Test Builder properties
    print("Testing Builder properties...");
    assert_true(builder.container_id != "", "Container ID should not be empty");
    assert_eq(builder.name, "rhai_test_container", "Container name should match");
    assert_eq(builder.image, "alpine:latest", "Image name should match");
    
    // Test debug mode
    print("Testing debug mode...");
    assert_true(!builder.debug_mode, "Debug mode should be off by default");
    builder.debug_mode = true;
    assert_true(builder.debug_mode, "Debug mode should be on after setting");
    
    // Test running a command
    print("Testing run()...");
    let result = builder.run("echo 'Hello from container'");
    assert_true(result.success, "Command should succeed");
    assert_true(result.stdout.contains("Hello from container"), "Command output should contain expected text");
    print("✓ run(): Command executed successfully");
    
    // Test writing content to a file in the container
    print("Testing write_content()...");
    let content = "Hello from a file";
    builder.write_content(content, "/test_file.txt");
    
    // Verify the content was written
    let read_result = builder.run("cat /test_file.txt");
    assert_true(read_result.success, "Command should succeed");
    assert_true(read_result.stdout.contains(content), "File content should match what was written");
    print("✓ write_content(): Content written successfully");
    
    // Test reading content from a file in the container
    print("Testing read_content()...");
    let read_content = builder.read_content("/test_file.txt");
    assert_true(read_content.contains(content), "Read content should match what was written");
    print("✓ read_content(): Content read successfully");
    
    // Test setting entrypoint
    print("Testing set_entrypoint()...");
    let entrypoint = ["/bin/sh", "-c"];
    builder.set_entrypoint(entrypoint);
    print("✓ set_entrypoint(): Entrypoint set successfully");
    
    // Test setting cmd
    print("Testing set_cmd()...");
    let cmd = ["echo", "Hello from CMD"];
    builder.set_cmd(cmd);
    print("✓ set_cmd(): CMD set successfully");
    
    // Test adding a file
    print("Testing add()...");
    // Create a test file
    file_write("test_add_file.txt", "Test content for add");
    builder.add("test_add_file.txt", "/");
    
    // Verify the file was added
    let add_result = builder.run("cat /test_add_file.txt");
    assert_true(add_result.success, "Command should succeed");
    assert_true(add_result.stdout.contains("Test content for add"), "Added file content should match");
    print("✓ add(): File added successfully");
    
    // Test copying a file
    print("Testing copy()...");
    // Create a test file
    file_write("test_copy_file.txt", "Test content for copy");
    builder.copy("test_copy_file.txt", "/");
    
    // Verify the file was copied
    let copy_result = builder.run("cat /test_copy_file.txt");
    assert_true(copy_result.success, "Command should succeed");
    assert_true(copy_result.stdout.contains("Test content for copy"), "Copied file content should match");
    print("✓ copy(): File copied successfully");
    
    // Test committing to an image
    print("Testing commit()...");
    let image_name = "rhai_test_image:latest";
    builder.commit(image_name);
    print("✓ commit(): Container committed to image successfully");
    
    // Test removing the container
    print("Testing remove()...");
    builder.remove();
    print("✓ remove(): Container removed successfully");
    
    // Clean up test files
    delete("test_add_file.txt");
    delete("test_copy_file.txt");
    
    // Test image operations
    print("Testing image operations...");
    
    // Test listing images
    print("Testing images()...");
    let images = builder.images();
    assert_true(images.len() > 0, "There should be at least one image");
    print("✓ images(): Images listed successfully");
    
    // Test removing the image
    print("Testing image_remove()...");
    builder.image_remove(image_name);
    print("✓ image_remove(): Image removed successfully");
    
    print("All Builder pattern tests completed successfully!");
} catch(err) {
    print(`Error: ${err}`);
    
    // Clean up in case of error
    try {
        // Remove test container if it exists
        run("buildah rm rhai_test_container");
    } catch(_) {}
    
    try {
        // Remove test image if it exists
        run("buildah rmi rhai_test_image:latest");
    } catch(_) {}
    
    try {
        // Remove test files if they exist
        delete("test_add_file.txt");
        delete("test_copy_file.txt");
    } catch(_) {}
    
    throw err;
}
