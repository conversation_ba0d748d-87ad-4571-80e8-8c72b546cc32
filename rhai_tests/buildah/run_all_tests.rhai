// run_all_tests.rhai
// Runs all Buildah module tests

print("=== Running Buildah Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if buildah is available
fn is_buildah_available() {
    try {
        let result = run("which buildah");
        return result.success;
    } catch(e) {
        return false;
    }
}

// Run each test directly
let passed = 0;
let failed = 0;
let skipped = 0;
let total = 0;

// Check if buildah is available
let buildah_available = is_buildah_available();
if !buildah_available {
    print("Buildah is not available. Skipping all Buildah tests.");
    skipped = 3; // Skip all three tests
    total = 3;
} else {
    // Test 1: Builder Pattern
    print("\n--- Running Builder Pattern Tests ---");
    try {
        // Create a builder
        let builder = bah_new("rhai_test_container", "alpine:latest");
        
        // Test basic properties
        assert_true(builder.container_id != "", "Container ID should not be empty");
        assert_true(builder.name == "rhai_test_container", "Container name should match");
        
        // Run a simple command
        let result = builder.run("echo 'Hello from container'");
        assert_true(result.success, "Command should succeed");
        
        // Clean up
        builder.remove();
        
        print("--- Builder Pattern Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Builder Pattern Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        try {
            run("buildah rm rhai_test_container");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
    
    // Test 2: Image Operations
    print("\n--- Running Image Operations Tests ---");
    try {
        // Create a temporary directory for testing
        let test_dir = "rhai_test_buildah";
        mkdir(test_dir);
        
        // Create a builder
        let builder = bah_new("rhai_test_container", "alpine:latest");
        
        // List images
        let images = builder.images();
        assert_true(images.len() > 0, "There should be at least one image");
        
        // Clean up
        builder.remove();
        delete(test_dir);
        
        print("--- Image Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Image Operations Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        try {
            run("buildah rm rhai_test_container");
            delete("rhai_test_buildah");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
    
    // Test 3: Container Operations
    print("\n--- Running Container Operations Tests ---");
    try {
        // Create a builder
        let builder = bah_new("rhai_test_container", "alpine:latest");
        
        // Test reset
        builder.reset();
        
        // Create a new container
        builder = bah_new("rhai_test_container", "alpine:latest");
        
        // Run a command
        let result = builder.run("echo 'Hello from container'");
        assert_true(result.success, "Command should succeed");
        
        // Clean up
        builder.remove();
        
        print("--- Container Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Container Operations Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        try {
            run("buildah rm rhai_test_container");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Skipped: ${skipped}`);
print(`Total: ${total}`);

if failed == 0 {
    if skipped > 0 {
        print("\n⚠️ All tests skipped or passed!");
    } else {
        print("\n✅ All tests passed!");
    }
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
