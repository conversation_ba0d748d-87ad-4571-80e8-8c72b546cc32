// 03_container_operations.rhai
// Tests for Buildah container operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

// Helper function to check if buildah is available
fn is_buildah_available() {
    try {
        let result = run("which buildah");
        return result.success;
    } catch(err) {
        return false;
    }
}

print("=== Testing Buildah Container Operations ===");

// Check if buildah is available
let buildah_available = is_buildah_available();
if !buildah_available {
    print("Buildah is not available. Skipping Buildah tests.");
    // Exit gracefully without error
    return;
}

print("✓ Buildah is available");

try {
    // Test creating a new Builder
    print("Testing bah_new() and reset()...");
    let builder = bah_new("rhai_test_container", "alpine:latest");
    
    // Enable debug mode
    builder.debug_mode = true;
    
    // Test reset
    print("Testing reset()...");
    builder.reset();
    print("✓ reset(): Container reset successfully");
    
    // Create a new container
    builder = bah_new("rhai_test_container", "alpine:latest");
    
    // Test config
    print("Testing config()...");
    let config_options = #{
        "LABEL": "rhai_test=true",
        "ENV": "TEST_VAR=test_value"
    };
    builder.config(config_options);
    print("✓ config(): Container configured successfully");
    
    // Test run with isolation
    print("Testing run_with_isolation()...");
    let isolation_result = builder.run_with_isolation("echo 'Hello with isolation'", "oci");
    assert_true(isolation_result.success, "Command with isolation should succeed");
    assert_true(isolation_result.stdout.contains("Hello with isolation"), "Command output should contain expected text");
    print("✓ run_with_isolation(): Command executed successfully");
    
    // Test content operations
    print("Testing content operations...");
    
    // Write content to a file
    let script_content = `#!/bin/sh
echo "Hello from script"
`;
    builder.write_content(script_content, "/script.sh");
    
    // Make the script executable
    builder.run("chmod +x /script.sh");
    
    // Run the script
    let script_result = builder.run("/script.sh");
    assert_true(script_result.success, "Script should execute successfully");
    assert_true(script_result.stdout.contains("Hello from script"), "Script output should contain expected text");
    print("✓ Content operations: Script created and executed successfully");
    
    // Test commit with config
    print("Testing commit with config...");
    let commit_options = #{
        "author": "Rhai Test",
        "message": "Test commit"
    };
    builder.commit("rhai_test_commit:latest", commit_options);
    print("✓ commit(): Container committed with config successfully");
    
    // Clean up
    builder.remove();
    print("✓ Cleanup: Container removed");
    
    // Remove the committed image
    builder.image_remove("rhai_test_commit:latest");
    print("✓ Cleanup: Committed image removed");
    
    print("All container operations tests completed successfully!");
} catch(err) {
    print(`Error: ${err}`);
    
    // Clean up in case of error
    try {
        // Remove test container if it exists
        run("buildah rm rhai_test_container");
    } catch(_) {}
    
    try {
        // Remove test image if it exists
        run("buildah rmi rhai_test_commit:latest");
    } catch(_) {}
    
    throw err;
}
