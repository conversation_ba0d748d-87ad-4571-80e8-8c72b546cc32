// 01_redis_connection.rhai
// Tests for Redis client connection and basic operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if Redis is available
fn is_redis_available() {
    try {
        // Try to execute a simple PING command
        let ping_result = redis_ping();
        return ping_result == "PONG";
    } catch(err) {
        print(`Redis connection error: ${err}`);
        return false;
    }
}

print("=== Testing Redis Client Connection ===");

// Check if Redis is available
let redis_available = is_redis_available();
if !redis_available {
    print("Redis server is not available. Skipping Redis tests.");
    // Exit gracefully without error
    return;
}

print("✓ Redis server is available");

// Test redis_ping function
print("Testing redis_ping()...");
let ping_result = redis_ping();
assert_true(ping_result == "PONG", "PING should return PONG");
print(`✓ redis_ping(): Returned ${ping_result}`);

// Test redis_set and redis_get functions
print("Testing redis_set() and redis_get()...");
let test_key = "rhai_test_key";
let test_value = "Hello from Rhai test";

// Set a value
let set_result = redis_set(test_key, test_value);
assert_true(set_result, "SET operation should succeed");
print(`✓ redis_set(): Successfully set key ${test_key}`);

// Get the value back
let get_result = redis_get(test_key);
assert_true(get_result == test_value, "GET should return the value we set");
print(`✓ redis_get(): Successfully retrieved value for key ${test_key}`);

// Test redis_del function
print("Testing redis_del()...");
let del_result = redis_del(test_key);
assert_true(del_result, "DEL operation should succeed");
print(`✓ redis_del(): Successfully deleted key ${test_key}`);

// Verify the key was deleted
let get_after_del = redis_get(test_key);
assert_true(get_after_del == "", "Key should not exist after deletion");
print("✓ Key was successfully deleted");

print("All Redis connection tests completed successfully!");
