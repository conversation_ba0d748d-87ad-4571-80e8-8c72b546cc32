// 02_redis_operations.rhai
// Tests for advanced Redis operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if Redis is available
fn is_redis_available() {
    try {
        // Try to execute a simple PING command
        let ping_result = redis_ping();
        return ping_result == "PONG";
    } catch(err) {
        print(`Redis connection error: ${err}`);
        return false;
    }
}

print("=== Testing Advanced Redis Operations ===");

// Check if Redis is available
let redis_available = is_redis_available();
if !redis_available {
    print("Redis server is not available. Skipping Redis tests.");
    // Exit gracefully without error
    return;
}

print("✓ Redis server is available");

// Test prefix for all keys to avoid conflicts
let prefix = "rhai_test_";

// Test redis_hset and redis_hget functions
print("Testing redis_hset() and redis_hget()...");
let hash_key = prefix + "hash";
let field1 = "field1";
let value1 = "value1";
let field2 = "field2";
let value2 = "value2";

// Set hash fields
let hset_result1 = redis_hset(hash_key, field1, value1);
assert_true(hset_result1, "HSET operation should succeed for field1");
let hset_result2 = redis_hset(hash_key, field2, value2);
assert_true(hset_result2, "HSET operation should succeed for field2");
print(`✓ redis_hset(): Successfully set fields in hash ${hash_key}`);

// Get hash fields
let hget_result1 = redis_hget(hash_key, field1);
assert_true(hget_result1 == value1, "HGET should return the value we set for field1");
let hget_result2 = redis_hget(hash_key, field2);
assert_true(hget_result2 == value2, "HGET should return the value we set for field2");
print(`✓ redis_hget(): Successfully retrieved values from hash ${hash_key}`);

// Test redis_hgetall function
print("Testing redis_hgetall()...");
let hgetall_result = redis_hgetall(hash_key);
assert_true(hgetall_result.len() == 2, "HGETALL should return 2 fields");
assert_true(hgetall_result[field1] == value1, "HGETALL should include field1 with correct value");
assert_true(hgetall_result[field2] == value2, "HGETALL should include field2 with correct value");
print(`✓ redis_hgetall(): Successfully retrieved all fields from hash ${hash_key}`);

// Test redis_hdel function
print("Testing redis_hdel()...");
let hdel_result = redis_hdel(hash_key, field1);
assert_true(hdel_result, "HDEL operation should succeed");
print(`✓ redis_hdel(): Successfully deleted field from hash ${hash_key}`);

// Verify the field was deleted
let hget_after_del = redis_hget(hash_key, field1);
assert_true(hget_after_del == "", "Field should not exist after deletion");
print("✓ Field was successfully deleted from hash");

// Test redis_list operations
print("Testing redis list operations...");
let list_key = prefix + "list";

// Push items to list
let rpush_result = redis_rpush(list_key, "item1");
assert_true(rpush_result > 0, "RPUSH operation should succeed");
redis_rpush(list_key, "item2");
redis_rpush(list_key, "item3");
print(`✓ redis_rpush(): Successfully pushed items to list ${list_key}`);

// Get list length
let llen_result = redis_llen(list_key);
assert_true(llen_result == 3, "List should have 3 items");
print(`✓ redis_llen(): List has ${llen_result} items`);

// Get list range
let lrange_result = redis_lrange(list_key, 0, -1);
assert_true(lrange_result.len() == 3, "LRANGE should return 3 items");
assert_true(lrange_result[0] == "item1", "First item should be 'item1'");
assert_true(lrange_result[2] == "item3", "Last item should be 'item3'");
print(`✓ redis_lrange(): Successfully retrieved all items from list ${list_key}`);

// Clean up
print("Cleaning up...");
redis_del(hash_key);
redis_del(list_key);
print("✓ Cleanup: All test keys removed");

print("All Redis operations tests completed successfully!");
