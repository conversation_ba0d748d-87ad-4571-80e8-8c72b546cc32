#!/bin/bash

# Run all Rhai tests
# This script runs all the Rhai tests in the rhai_tests directory

# Set the base directory
BASE_DIR="."

# Path to herodo executable (assuming debug build)
HERODO_CMD="$HOME/hero/bin/herodo"

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Initialize counters
TOTAL_MODULES=0
PASSED_MODULES=0
FAILED_MODULES=0

# Function to run tests in a directory
run_tests_in_dir() {
    local dir=$1
    local module_name=$(basename $dir)
    
    echo -e "${YELLOW}Running tests for module: ${module_name}${NC}"
    
    # Check if the directory has a run_all_tests.rhai script
    if [ -f "${dir}/run_all_tests.rhai" ]; then
        echo "Using module's run_all_tests.rhai script"
        ${HERODO_CMD} --path "${dir}/run_all_tests.rhai"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ All tests passed for module: ${module_name}${NC}"
            PASSED_MODULES=$((PASSED_MODULES + 1))
        else
            echo -e "${RED}✗ Tests failed for module: ${module_name}${NC}"
            FAILED_MODULES=$((FAILED_MODULES + 1))
        fi
    else
        # Run all .rhai files in the directory
        local test_files=$(find "${dir}" -name "*.rhai" | sort)
        local all_passed=true
        
        for test_file in $test_files; do
            echo "Running test: $(basename $test_file)"
            ${HERODO_CMD} --path "$test_file"
            
            if [ $? -ne 0 ]; then
                all_passed=false
            fi
        done
        
        if $all_passed; then
            echo -e "${GREEN}✓ All tests passed for module: ${module_name}${NC}"
            PASSED_MODULES=$((PASSED_MODULES + 1))
        else
            echo -e "${RED}✗ Tests failed for module: ${module_name}${NC}"
            FAILED_MODULES=$((FAILED_MODULES + 1))
        fi
    fi
    
    TOTAL_MODULES=$((TOTAL_MODULES + 1))
    echo ""
}

# Main function
main() {
    echo "=======================================
            Running Rhai Tests              
======================================="
    
    # Find all module directories
    for dir in $(find "${BASE_DIR}" -mindepth 1 -maxdepth 1 -type d | sort); do
        run_tests_in_dir "$dir"
    done
    
    # Print summary
    echo "=======================================
            Test Summary              
======================================="
    echo "Total modules tested: ${TOTAL_MODULES}"
    echo "Passed: ${PASSED_MODULES}"
    echo "Failed: ${FAILED_MODULES}"
    
    if [ $FAILED_MODULES -gt 0 ]; then
        echo -e "${RED}Some tests failed!${NC}"
        exit 1
    else
        echo -e "${GREEN}All tests passed!${NC}"
        exit 0
    fi
}

# Run the main function
main
