// run_all_tests.rhai
// Runs all OS module tests

print("=== Running OS Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Run each test directly
let passed = 0;
let failed = 0;

// Test 1: File Operations
print("\n--- Running File Operations Tests ---");
try {
    // Create a test directory structure
    let test_dir = "rhai_test_fs";
    let sub_dir = test_dir + "/subdir";

    // Test mkdir function
    print("Testing mkdir...");
    let mkdir_result = mkdir(test_dir);
    assert_true(exist(test_dir), "Directory creation failed");
    print(`✓ mkdir: ${mkdir_result}`);

    // Test nested directory creation
    let nested_result = mkdir(sub_dir);
    assert_true(exist(sub_dir), "Nested directory creation failed");
    print(`✓ mkdir (nested): ${nested_result}`);

    // Test file_write function
    let test_file = test_dir + "/test.txt";
    let file_content = "This is a test file created by Rhai test script.";
    let write_result = file_write(test_file, file_content);
    assert_true(exist(test_file), "File creation failed");
    print(`✓ file_write: ${write_result}`);

    // Test file_read function
    let read_content = file_read(test_file);
    assert_true(read_content == file_content, "File content doesn't match");
    print(`✓ file_read: Content matches`);

    // Test file_size function
    let size = file_size(test_file);
    assert_true(size > 0, "File size should be greater than 0");
    print(`✓ file_size: ${size} bytes`);

    // Clean up
    delete(test_file);
    delete(sub_dir);
    delete(test_dir);
    assert_true(!exist(test_dir), "Directory deletion failed");
    print(`✓ delete: Directory cleaned up`);

    print("--- File Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in File Operations Tests: ${err}`);
    failed += 1;
}

// Test 2: Download Operations
print("\n--- Running Download Operations Tests ---");
try {
    // Create a test directory
    let test_dir = "rhai_test_download";
    mkdir(test_dir);
    print(`Created test directory: ${test_dir}`);

    // Test which function to ensure curl is available
    let curl_path = which("curl");
    if curl_path == "" {
        print("Warning: curl not found, download tests may fail");
    } else {
        print(`✓ which: curl found at ${curl_path}`);
    }

    // Test cmd_ensure_exists function
    let ensure_result = cmd_ensure_exists("curl");
    print(`✓ cmd_ensure_exists: ${ensure_result}`);

    // Test download function with a small file
    let download_url = "https://raw.githubusercontent.com/rust-lang/rust/master/LICENSE-MIT";
    let download_dest = test_dir + "/license.txt";
    let min_size_kb = 1; // Minimum size in KB

    print(`Downloading ${download_url}...`);
    let download_result = download_file(download_url, download_dest, min_size_kb);
    assert_true(exist(download_dest), "Download failed");
    print(`✓ download_file: ${download_result}`);

    // Verify the downloaded file
    let file_content = file_read(download_dest);
    assert_true(file_content.contains("Permission is hereby granted"), "Downloaded file content is incorrect");
    print("✓ Downloaded file content verified");

    // Clean up
    delete(test_dir);
    assert_true(!exist(test_dir), "Directory deletion failed");
    print(`✓ Cleanup: Directory ${test_dir} removed`);

    print("--- Download Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Download Operations Tests: ${err}`);
    failed += 1;
}

// Test 3: Package Operations
print("\n--- Running Package Operations Tests ---");
try {
    // Test package_platform function
    let platform = package_platform();
    print(`Current platform: ${platform}`);

    // Test package_set_debug function
    let debug_enabled = package_set_debug(true);
    assert_true(debug_enabled, "Debug mode should be enabled");
    print("✓ package_set_debug: Debug mode enabled");

    // Disable debug mode for remaining tests
    package_set_debug(false);

    print("--- Package Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Package Operations Tests: ${err}`);
    failed += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Total: ${passed + failed}`);

if failed == 0 {
    print("\n✅ All tests passed!");
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
