// 03_package_operations.rhai
// Tests for package management operations in the OS module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Test package_platform function
let platform = package_platform();
print(`Current platform: ${platform}`);

// Test package_set_debug function
let debug_enabled = package_set_debug(true);
assert_true(debug_enabled, "Debug mode should be enabled");
print("✓ package_set_debug: Debug mode enabled");

// Disable debug mode for remaining tests
package_set_debug(false);

// Test package_is_installed function with a package that should exist on most systems
let common_packages = ["bash", "curl", "grep"];
let found_package = false;

for pkg in common_packages {
    let is_installed = package_is_installed(pkg);
    if is_installed {
        print(`✓ package_is_installed: ${pkg} is installed`);
        found_package = true;
        break;
    }
}

if !found_package {
    print("Warning: None of the common packages were found installed");
}

// Test package_search function with a common term
// Note: This might be slow and produce a lot of output
print("Testing package_search (this might take a moment)...");
let search_results = package_search("lib");
print(`✓ package_search: Found ${search_results.len()} packages containing 'lib'`);

// Test package_list function
// Note: This might be slow and produce a lot of output
print("Testing package_list (this might take a moment)...");
let installed_packages = package_list();
print(`✓ package_list: Found ${installed_packages.len()} installed packages`);

// Note: We're not testing package_install, package_remove, package_update, or package_upgrade
// as they require root privileges and could modify the system state

print("All package management tests completed successfully!");
