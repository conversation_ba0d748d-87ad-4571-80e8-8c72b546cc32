// run_all_tests.rhai
// Runs all Git module tests

print("=== Running Git Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Run each test directly
let passed = 0;
let failed = 0;

// Test 1: Basic Git Operations
print("\n--- Running Basic Git Operations Tests ---");
try {
    // Create a temporary directory for Git operations
    let test_dir = "rhai_test_git";
    mkdir(test_dir);
    print(`Created test directory: ${test_dir}`);

    // Test GitTree constructor
    print("Testing GitTree constructor...");
    let git_tree = git_tree_new(test_dir);
    print("✓ GitTree created successfully");

    // Test GitTree.list() with empty directory
    print("Testing GitTree.list() with empty directory...");
    let repos = git_tree.list();
    assert_true(repos.len() == 0, "Expected empty list of repositories");
    print(`✓ GitTree.list(): Found ${repos.len()} repositories (expected 0)`);

    // Test GitTree.find() with empty directory
    print("Testing GitTree.find() with empty directory...");
    let found_repos = git_tree.find("*");
    assert_true(found_repos.len() == 0, "Expected empty list of repositories");
    print(`✓ GitTree.find(): Found ${found_repos.len()} repositories (expected 0)`);

    // Clean up
    print("Cleaning up...");
    delete(test_dir);
    assert_true(!exist(test_dir), "Directory deletion failed");
    print(`✓ Cleanup: Directory ${test_dir} removed`);

    print("--- Basic Git Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Basic Git Operations Tests: ${err}`);
    failed += 1;
}

// Test 2: Git Repository Operations
print("\n--- Running Git Repository Operations Tests ---");
try {
    // Create a temporary directory for Git operations
    let test_dir = "rhai_test_git_ops";
    mkdir(test_dir);
    print(`Created test directory: ${test_dir}`);

    // Create a GitTree
    print("Creating GitTree...");
    let git_tree = git_tree_new(test_dir);
    print("✓ GitTree created successfully");

    // Clean up
    print("Cleaning up...");
    delete(test_dir);
    assert_true(!exist(test_dir), "Directory deletion failed");
    print(`✓ Cleanup: Directory ${test_dir} removed`);

    print("--- Git Repository Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Git Repository Operations Tests: ${err}`);
    failed += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Total: ${passed + failed}`);

if failed == 0 {
    print("\n✅ All tests passed!");
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
