// run_all_tests.rhai
// Runs all Nerdctl module tests

print("=== Running Nerdctl Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if nerdctl is available
fn is_nerdctl_available() {
    try {
        let result = run("which nerdctl");
        return result.success;
    } catch(e) {
        return false;
    }
}

// Helper function to clean up a container if it exists
fn cleanup_container(container_name) {
    try {
        run(`nerdctl stop ${container_name}`);
        run(`nerdctl rm ${container_name}`);
    } catch(e) {
        // Ignore errors during cleanup
    }
}

// Run each test directly
let passed = 0;
let failed = 0;
let skipped = 0;
let total = 0;

// Check if nerdctl is available
let nerdctl_available = is_nerdctl_available();
if !nerdctl_available {
    print("nerdctl is not available. Skipping all Nerdctl tests.");
    skipped = 3; // Skip all three tests
    total = 3;
} else {
    // Test 1: Container Operations
    print("\n--- Running Container Operations Tests ---");
    try {
        // Define test container name
        let container_name = "rhai_test_container";
        
        // Clean up any existing test container
        cleanup_container(container_name);
        
        // Create a new Container
        let container = nerdctl_container_new(container_name);
        
        // Set container image
        container.with_image("alpine:latest");
        
        // Set detach mode
        container.with_detach(true);
        
        // Run the container
        let run_result = container.run();
        assert_true(run_result.success, "Container run should succeed");
        
        // Execute a command in the container
        let exec_result = container.exec("echo 'Hello from container'");
        assert_true(exec_result.success, "Container exec should succeed");
        
        // Clean up
        container.stop();
        container.remove();
        
        print("--- Container Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Container Operations Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        cleanup_container("rhai_test_container");
    }
    total += 1;
    
    // Test 2: Image Operations
    print("\n--- Running Image Operations Tests ---");
    try {
        // Create a temporary directory for testing
        let test_dir = "rhai_test_nerdctl";
        mkdir(test_dir);
        
        // Pull a small image for testing
        let pull_result = nerdctl_image_pull("alpine:latest");
        assert_true(pull_result.success, "Image pull should succeed");
        
        // List images
        let images_result = nerdctl_images();
        assert_true(images_result.success, "Image listing should succeed");
        
        // Clean up
        delete(test_dir);
        
        print("--- Image Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Image Operations Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        try {
            delete("rhai_test_nerdctl");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
    
    // Test 3: Container Builder Pattern
    print("\n--- Running Container Builder Pattern Tests ---");
    try {
        // Define test container name
        let container_name = "rhai_test_builder";
        
        // Clean up any existing test container
        cleanup_container(container_name);
        
        // Create test directory
        let work_dir = "rhai_test_nerdctl_work";
        mkdir(work_dir);
        
        // Create a container with builder pattern
        let container = nerdctl_container_from_image(container_name, "alpine:latest")
            .reset()
            .with_detach(true)
            .with_volumes([`${work_dir}:/data`]);
        
        // Run the container
        let run_result = container.run();
        assert_true(run_result.success, "Container run should succeed");
        
        // Clean up
        container.stop();
        container.remove();
        delete(work_dir);
        
        print("--- Container Builder Pattern Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Container Builder Pattern Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        cleanup_container("rhai_test_builder");
        try {
            delete("rhai_test_nerdctl_work");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Skipped: ${skipped}`);
print(`Total: ${total}`);

if failed == 0 {
    if skipped > 0 {
        print("\n⚠️ All tests skipped or passed!");
    } else {
        print("\n✅ All tests passed!");
    }
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
