// 02_image_operations.rhai
// Tests for Nerdctl image operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

// Helper function to check if nerdctl is available
fn is_nerdctl_available() {
    try {
        let result = run("which nerdctl");
        return result.success;
    } catch(err) {
        return false;
    }
}

// Helper function to check if an image exists
fn image_exists(image_name) {
    try {
        let result = run(`nerdctl images -q ${image_name}`);
        return result.success && result.stdout.trim() != "";
    } catch(err) {
        return false;
    }
}

// Helper function to clean up an image if it exists
fn cleanup_image(image_name) {
    if image_exists(image_name) {
        try {
            run(`nerdctl rmi ${image_name}`);
            print(`Cleaned up image: ${image_name}`);
        } catch(err) {
            print(`Error cleaning up image ${image_name}: ${err}`);
        }
    }
}

print("=== Testing Nerdctl Image Operations ===");

// Check if nerdctl is available
let nerdctl_available = is_nerdctl_available();
if !nerdctl_available {
    print("nerdctl is not available. Skipping Nerdctl tests.");
    // Exit gracefully without error
    return;
}

print("✓ nerdctl is available");

// Create a temporary directory for testing
let test_dir = "rhai_test_nerdctl";
mkdir(test_dir);

try {
    // Test pulling an image
    print("Testing nerdctl_image_pull()...");
    // Use a small image for testing
    let pull_result = nerdctl_image_pull("alpine:latest");
    assert_true(pull_result.success, "Image pull should succeed");
    print("✓ nerdctl_image_pull(): Image pulled successfully");
    
    // Test listing images
    print("Testing nerdctl_images()...");
    let images_result = nerdctl_images();
    assert_true(images_result.success, "Image listing should succeed");
    assert_true(images_result.stdout.contains("alpine"), "Image list should contain alpine");
    print("✓ nerdctl_images(): Images listed successfully");
    
    // Test tagging an image
    print("Testing nerdctl_image_tag()...");
    let tag_result = nerdctl_image_tag("alpine:latest", "rhai_test_image:latest");
    assert_true(tag_result.success, "Image tag should succeed");
    print("✓ nerdctl_image_tag(): Image tagged successfully");
    
    // Test building an image
    print("Testing nerdctl_image_build()...");
    
    // Create a simple Dockerfile
    let dockerfile_content = `FROM alpine:latest
RUN echo "Hello from Dockerfile" > /hello.txt
CMD ["cat", "/hello.txt"]
`;
    file_write(`${test_dir}/Dockerfile`, dockerfile_content);
    
    // Build the image
    let build_result = nerdctl_image_build("rhai_test_build:latest", test_dir);
    assert_true(build_result.success, "Image build should succeed");
    print("✓ nerdctl_image_build(): Image built successfully");
    
    // Test running a container from the built image
    print("Testing container from built image...");
    let container_name = "rhai_test_container_from_build";
    
    // Clean up any existing container with the same name
    try {
        run(`nerdctl stop ${container_name}`);
        run(`nerdctl rm ${container_name}`);
    } catch(e) {
        // Ignore errors during cleanup
    }
    
    // Run the container
    let run_result = nerdctl_run_with_name("rhai_test_build:latest", container_name);
    assert_true(run_result.success, "Container run should succeed");
    assert_true(run_result.stdout.contains("Hello from Dockerfile"), "Container output should contain expected text");
    print("✓ Container from built image ran successfully");
    
    // Clean up the container
    let stop_result = nerdctl_stop(container_name);
    assert_true(stop_result.success, "Container stop should succeed");
    let remove_result = nerdctl_remove(container_name);
    assert_true(remove_result.success, "Container remove should succeed");
    print("✓ Cleanup: Container removed");
    
    // Test removing images
    print("Testing nerdctl_image_remove()...");
    
    // Remove the tagged image
    let remove_tag_result = nerdctl_image_remove("rhai_test_image:latest");
    assert_true(remove_tag_result.success, "Image removal should succeed");
    print("✓ nerdctl_image_remove(): Tagged image removed successfully");
    
    // Remove the built image
    let remove_build_result = nerdctl_image_remove("rhai_test_build:latest");
    assert_true(remove_build_result.success, "Image removal should succeed");
    print("✓ nerdctl_image_remove(): Built image removed successfully");
    
    print("All image operations tests completed successfully!");
} catch(err) {
    print(`Error: ${err}`);
    
    // Clean up in case of error
    try {
        run("nerdctl stop rhai_test_container_from_build");
        run("nerdctl rm rhai_test_container_from_build");
    } catch(e) {
        // Ignore errors during cleanup
    }
    
    try {
        cleanup_image("rhai_test_image:latest");
        cleanup_image("rhai_test_build:latest");
    } catch(e) {
        // Ignore errors during cleanup
    }
    
    throw err;
} finally {
    // Clean up test directory
    delete(test_dir);
    print("✓ Cleanup: Test directory removed");
}
