// 01_mount_operations.rhai
// Tests for RFS mount operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

// Helper function to check if rfs is available
fn is_rfs_available() {
    try {
        let result = run("which rfs");
        return result.success;
    } catch(err) {
        return false;
    }
}

// Helper function to clean up mounts
fn cleanup_mounts() {
    try {
        rfs_unmount_all();
        print("All mounts cleaned up");
    } catch(err) {
        print(`Error cleaning up mounts: ${err}`);
    }
}

print("=== Testing RFS Mount Operations ===");

// Check if rfs is available
let rfs_available = is_rfs_available();
if !rfs_available {
    print("rfs is not available. Skipping RFS tests.");
    // Exit gracefully without error
    return;
}

print("✓ rfs is available");

// Clean up any existing mounts
cleanup_mounts();

// Create test directories
let source_dir = "rhai_test_rfs_source";
let target_dir = "rhai_test_rfs_target";
mkdir(source_dir);
mkdir(target_dir);

// Create a test file in the source directory
let test_file = `${source_dir}/test.txt`;
file_write(test_file, "Hello from RFS test");

try {
    // Test mounting a local directory
    print("Testing rfs_mount() with local directory...");
    let options = #{
        "readonly": "true"
    };
    
    let mount = rfs_mount(source_dir, target_dir, "local", options);
    
    // Verify mount properties
    assert_true(mount.id != "", "Mount ID should not be empty");
    assert_eq(mount.source, source_dir, "Mount source should match");
    assert_eq(mount.target, target_dir, "Mount target should match");
    assert_eq(mount.fs_type, "local", "Mount type should be local");
    print(`✓ rfs_mount(): Mounted ${mount.source} to ${mount.target} with ID: ${mount.id}`);
    
    // Test listing mounts
    print("Testing rfs_list_mounts()...");
    let mounts = rfs_list_mounts();
    assert_true(mounts.len() > 0, "There should be at least one mount");
    
    // Find our mount in the list
    let found = false;
    for m in mounts {
        if m.target == target_dir {
            found = true;
            break;
        }
    }
    assert_true(found, "Our mount should be in the list");
    print(`✓ rfs_list_mounts(): Found ${mounts.len()} mounts`);
    
    // Test getting mount info
    print("Testing rfs_get_mount_info()...");
    let mount_info = rfs_get_mount_info(target_dir);
    assert_eq(mount_info.target, target_dir, "Mount info target should match");
    assert_eq(mount_info.source, source_dir, "Mount info source should match");
    print(`✓ rfs_get_mount_info(): Got info for mount at ${mount_info.target}`);
    
    // Verify the mounted file is accessible
    let mounted_file = `${target_dir}/test.txt`;
    assert_true(exist(mounted_file), "Mounted file should exist");
    let mounted_content = file_read(mounted_file);
    assert_eq(mounted_content, "Hello from RFS test", "Mounted file content should match");
    print("✓ Mounted file is accessible and content matches");
    
    // Test unmounting a specific mount
    print("Testing rfs_unmount()...");
    rfs_unmount(target_dir);
    
    // Verify the mount is gone
    try {
        rfs_get_mount_info(target_dir);
        assert_true(false, "Mount should not exist after unmounting");
    } catch(err) {
        print("✓ rfs_unmount(): Mount successfully unmounted");
    }
    
    // Mount again to test unmount_all
    print("Testing mounting again for unmount_all...");
    let mount2 = rfs_mount(source_dir, target_dir, "local", options);
    assert_true(mount2.id != "", "Mount ID should not be empty");
    
    // Test unmounting all mounts
    print("Testing rfs_unmount_all()...");
    rfs_unmount_all();
    
    // Verify all mounts are gone
    let mounts_after = rfs_list_mounts();
    assert_true(mounts_after.len() == 0, "There should be no mounts after unmount_all");
    print("✓ rfs_unmount_all(): All mounts successfully unmounted");
    
    print("All mount operations tests completed successfully!");
} catch(err) {
    print(`Error: ${err}`);
    
    // Clean up in case of error
    cleanup_mounts();
    
    throw err;
} finally {
    // Clean up test directories
    delete(source_dir);
    delete(target_dir);
    print("✓ Cleanup: Test directories removed");
}
