// 02_filesystem_layer_operations.rhai
// Tests for RFS filesystem layer operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

// Helper function to check if rfs is available
fn is_rfs_available() {
    try {
        let result = run("which rfs");
        return result.success;
    } catch(err) {
        return false;
    }
}

print("=== Testing RFS Filesystem Layer Operations ===");

// Check if rfs is available
let rfs_available = is_rfs_available();
if !rfs_available {
    print("rfs is not available. Skipping RFS tests.");
    // Exit gracefully without error
    return;
}

print("✓ rfs is available");

// Create test directories
let source_dir = "rhai_test_rfs_source";
let unpack_dir = "rhai_test_rfs_unpack";
mkdir(source_dir);
mkdir(unpack_dir);

// Create test files in the source directory
file_write(`${source_dir}/file1.txt`, "Content of file 1");
file_write(`${source_dir}/file2.txt`, "Content of file 2");

// Create a subdirectory with files
mkdir(`${source_dir}/subdir`);
file_write(`${source_dir}/subdir/file3.txt`, "Content of file 3");

// Output file for the filesystem layer
let output_file = "rhai_test_rfs_layer.fl";

try {
    // Test packing a directory
    print("Testing rfs_pack()...");
    // Use a file store spec for testing
    let store_specs = "file:path=.";
    rfs_pack(source_dir, output_file, store_specs);
    
    // Verify the output file exists
    assert_true(exist(output_file), "Output file should exist");
    print(`✓ rfs_pack(): Directory packed to ${output_file}`);
    
    // Test listing contents of the filesystem layer
    print("Testing rfs_list_contents()...");
    let contents = rfs_list_contents(output_file);
    
    // Verify the contents include our files
    assert_true(contents.contains("file1.txt"), "Contents should include file1.txt");
    assert_true(contents.contains("file2.txt"), "Contents should include file2.txt");
    assert_true(contents.contains("subdir/file3.txt"), "Contents should include subdir/file3.txt");
    print("✓ rfs_list_contents(): Layer contents listed successfully");
    
    // Test verifying the filesystem layer
    print("Testing rfs_verify()...");
    let is_valid = rfs_verify(output_file);
    assert_true(is_valid, "Filesystem layer should be valid");
    print("✓ rfs_verify(): Layer verified successfully");
    
    // Test unpacking the filesystem layer
    print("Testing rfs_unpack()...");
    rfs_unpack(output_file, unpack_dir);
    
    // Verify the unpacked files exist and have the correct content
    assert_true(exist(`${unpack_dir}/file1.txt`), "Unpacked file1.txt should exist");
    assert_true(exist(`${unpack_dir}/file2.txt`), "Unpacked file2.txt should exist");
    assert_true(exist(`${unpack_dir}/subdir/file3.txt`), "Unpacked subdir/file3.txt should exist");
    
    let content1 = file_read(`${unpack_dir}/file1.txt`);
    let content2 = file_read(`${unpack_dir}/file2.txt`);
    let content3 = file_read(`${unpack_dir}/subdir/file3.txt`);
    
    assert_eq(content1, "Content of file 1", "Content of file1.txt should match");
    assert_eq(content2, "Content of file 2", "Content of file2.txt should match");
    assert_eq(content3, "Content of file 3", "Content of file3.txt should match");
    
    print("✓ rfs_unpack(): Layer unpacked successfully");
    
    print("All filesystem layer operations tests completed successfully!");
} catch(err) {
    print(`Error: ${err}`);
    throw err;
} finally {
    // Clean up test directories and files
    delete(source_dir);
    delete(unpack_dir);
    delete(output_file);
    print("✓ Cleanup: Test directories and files removed");
}
