// run_all_tests.rhai
// Runs all RFS module tests

print("=== Running RFS Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if rfs is available
fn is_rfs_available() {
    try {
        let result = run("which rfs");
        return result.success;
    } catch(e) {
        return false;
    }
}

// Helper function to clean up mounts
fn cleanup_mounts() {
    try {
        rfs_unmount_all();
    } catch(e) {
        // Ignore errors during cleanup
    }
}

// Run each test directly
let passed = 0;
let failed = 0;
let skipped = 0;
let total = 0;

// Check if rfs is available
let rfs_available = is_rfs_available();
if !rfs_available {
    print("rfs is not available. Skipping all RFS tests.");
    skipped = 2; // Skip both tests
    total = 2;
} else {
    // Test 1: Mount Operations
    print("\n--- Running Mount Operations Tests ---");
    try {
        // Clean up any existing mounts
        cleanup_mounts();
        
        // Create test directories
        let source_dir = "rhai_test_rfs_source";
        let target_dir = "rhai_test_rfs_target";
        mkdir(source_dir);
        mkdir(target_dir);
        
        // Create a test file in the source directory
        let test_file = `${source_dir}/test.txt`;
        file_write(test_file, "Hello from RFS test");
        
        // Mount the directory
        let options = #{
            "readonly": "true"
        };
        
        let mount = rfs_mount(source_dir, target_dir, "local", options);
        assert_true(mount.id != "", "Mount ID should not be empty");
        
        // List mounts
        let mounts = rfs_list_mounts();
        assert_true(mounts.len() > 0, "There should be at least one mount");
        
        // Unmount
        rfs_unmount(target_dir);
        
        // Clean up
        delete(source_dir);
        delete(target_dir);
        
        print("--- Mount Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Mount Operations Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        cleanup_mounts();
        try {
            delete("rhai_test_rfs_source");
            delete("rhai_test_rfs_target");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
    
    // Test 2: Filesystem Layer Operations
    print("\n--- Running Filesystem Layer Operations Tests ---");
    try {
        // Create test directories
        let source_dir = "rhai_test_rfs_source";
        let unpack_dir = "rhai_test_rfs_unpack";
        mkdir(source_dir);
        mkdir(unpack_dir);
        
        // Create test files in the source directory
        file_write(`${source_dir}/file1.txt`, "Content of file 1");
        
        // Output file for the filesystem layer
        let output_file = "rhai_test_rfs_layer.fl";
        
        // Pack the directory
        let store_specs = "file:path=.";
        rfs_pack(source_dir, output_file, store_specs);
        
        // List contents
        let contents = rfs_list_contents(output_file);
        assert_true(contents.contains("file1.txt"), "Contents should include file1.txt");
        
        // Verify the layer
        let is_valid = rfs_verify(output_file);
        assert_true(is_valid, "Filesystem layer should be valid");
        
        // Unpack the layer
        rfs_unpack(output_file, unpack_dir);
        
        // Clean up
        delete(source_dir);
        delete(unpack_dir);
        delete(output_file);
        
        print("--- Filesystem Layer Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Filesystem Layer Operations Tests: ${err}`);
        failed += 1;
        
        // Clean up in case of error
        try {
            delete("rhai_test_rfs_source");
            delete("rhai_test_rfs_unpack");
            delete("rhai_test_rfs_layer.fl");
        } catch(e) {
            // Ignore errors during cleanup
        }
    }
    total += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Skipped: ${skipped}`);
print(`Total: ${total}`);

if failed == 0 {
    if skipped > 0 {
        print("\n⚠️ All tests skipped or passed!");
    } else {
        print("\n✅ All tests passed!");
    }
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
