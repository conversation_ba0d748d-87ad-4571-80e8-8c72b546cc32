// run_all_tests.rhai
// Runs all Keypair module tests

print("=== Running Keypair Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Run each test directly
let passed = 0;
let failed = 0;
let test_results = #{};

// Test 1: Keypair Operations
print("\n--- Running Keypair Operations Tests ---");
try {
    // Clear any existing session
    clear_session();
    
    // Test creating a new keypair
    print("Testing keypair creation...");
    let keypair_name = "test_keypair";
    if create_key_space("test_space", "password") {
        print("✓ Key space created successfully");
        
        if create_keypair(keypair_name) {
            print("✓ Keypair created successfully");
            
            // Test getting the public key
            print("Testing public key retrieval...");
            if select_keypair(keypair_name) {
                let pub_key = keypair_pub_key();
                assert_true(pub_key.len() > 0, "Public key should not be empty");
                print(`✓ Public key retrieved: ${pub_key.len()} bytes`);
                
                // Test signing a message
                print("Testing message signing...");
                let message = "This is a test message to sign";
                let signature = keypair_sign(message);
                assert_true(signature.len() > 0, "Signature should not be empty");
                print(`✓ Message signed successfully: ${signature.len()} bytes`);
                
                // Test verifying a signature
                print("Testing signature verification...");
                let is_valid = keypair_verify(message, signature);
                assert_true(is_valid, "Signature should be valid");
                print("✓ Signature verified successfully");
            }
        }
    }
    
    print("--- Keypair Operations Tests completed successfully ---");
    passed += 1;
    test_results["01_keypair_operations"] = "PASSED";
} catch(err) {
    print(`!!! Error in Keypair Operations Tests: ${err}`);
    failed += 1;
    test_results["01_keypair_operations"] = `FAILED: ${err}`;
}

// Test 2: Key Space Operations
print("\n--- Running Key Space Operations Tests ---");
try {
    // Clear any existing session
    clear_session();
    
    // Test creating a new key space
    print("Testing key space creation...");
    let space_name = "test_keyspace";
    let password = "secure_password";

    if create_key_space(space_name, password) {
        print(`✓ Key space "${space_name}" created successfully`);
        
        // Test adding keypairs to a key space
        print("Testing adding keypairs to key space...");
        let keypair1_name = "keypair1";
        let keypair2_name = "keypair2";
        
        if create_keypair(keypair1_name) {
            print(`✓ Keypair "${keypair1_name}" created successfully`);
        }
        
        if create_keypair(keypair2_name) {
            print(`✓ Keypair "${keypair2_name}" created successfully`);
        }
        
        // Test listing keypairs in a key space
        print("Testing listing keypairs in key space...");
        let keypairs = list_keypairs();
        assert_true(keypairs.len() == 2, `Expected 2 keypairs, got ${keypairs.len()}`);
        assert_true(keypairs.contains(keypair1_name), `Keypair list should contain "${keypair1_name}"`);
        assert_true(keypairs.contains(keypair2_name), `Keypair list should contain "${keypair2_name}"`);
        print(`✓ Listed keypairs successfully: ${keypairs}`);
    }
    
    print("--- Key Space Operations Tests completed successfully ---");
    passed += 1;
    test_results["02_keyspace_operations"] = "PASSED";
} catch(err) {
    print(`!!! Error in Key Space Operations Tests: ${err}`);
    failed += 1;
    test_results["02_keyspace_operations"] = `FAILED: ${err}`;
}

// Test 3: Session Management
print("\n--- Running Session Management Tests ---");
try {
    // Clear any existing session
    clear_session();
    
    // Test creating a key space and setting it as current
    print("Testing key space creation and activation...");
    let space_name1 = "session_test_space1";
    let space_name2 = "session_test_space2";
    let password = "secure_password";

    // Create first key space
    if create_key_space(space_name1, password) {
        print(`✓ Key space "${space_name1}" created successfully`);
        
        // Test creating keypairs in the current space
        print("Testing creating keypairs in current space...");
        let keypair1_name = "session_keypair1";
        
        if create_keypair(keypair1_name) {
            print(`✓ Keypair "${keypair1_name}" created successfully in space "${space_name1}"`);
        }
        
        // Test selecting a keypair
        print("Testing selecting a keypair...");
        if select_keypair(keypair1_name) {
            print(`✓ Selected keypair "${keypair1_name}" successfully`);
        }
    }
    
    print("--- Session Management Tests completed successfully ---");
    passed += 1;
    test_results["03_session_management"] = "PASSED";
} catch(err) {
    print(`!!! Error in Session Management Tests: ${err}`);
    failed += 1;
    test_results["03_session_management"] = `FAILED: ${err}`;
}

// Test 4: Encryption and Decryption
print("\n--- Running Encryption and Decryption Tests ---");
try {
    // Clear any existing session
    clear_session();
    
    // Test creating keypairs for sender and recipient
    print("Setting up sender and recipient keypairs...");
    let space_name = "encryption_test_space";
    let password = "secure_password";
    let sender_name = "sender_keypair";
    let recipient_name = "recipient_keypair";

    if create_key_space(space_name, password) {
        print(`✓ Key space "${space_name}" created successfully`);
        
        // Create sender keypair
        if create_keypair(sender_name) {
            print(`✓ Sender keypair "${sender_name}" created successfully`);
        }
        
        // Create recipient keypair
        if create_keypair(recipient_name) {
            print(`✓ Recipient keypair "${recipient_name}" created successfully`);
        }
        
        // Get recipient's public key
        if select_keypair(recipient_name) {
            print(`✓ Selected recipient keypair "${recipient_name}" successfully`);
            let recipient_pub_key = keypair_pub_key();
            
            // Switch to sender keypair
            if select_keypair(sender_name) {
                print(`✓ Selected sender keypair "${sender_name}" successfully`);
                
                // Test encrypting a message with recipient's public key
                print("\nTesting encrypting a message...");
                let message = "This is a secret message for the recipient";
                let ciphertext = encrypt_asymmetric(recipient_pub_key, message);
                
                // Switch back to recipient keypair to decrypt
                if select_keypair(recipient_name) {
                    print(`✓ Switched back to recipient keypair "${recipient_name}" successfully`);
                    
                    // Test decrypting the message
                    print("Testing decrypting the message...");
                    let decrypted = decrypt_asymmetric(ciphertext);
                    assert_true(decrypted == message, "Decrypted message should match original");
                    print(`✓ Message decrypted successfully: "${decrypted}"`);
                }
            }
        }
    }
    
    print("--- Encryption and Decryption Tests completed successfully ---");
    passed += 1;
    test_results["04_encryption_decryption"] = "PASSED";
} catch(err) {
    print(`!!! Error in Encryption and Decryption Tests: ${err}`);
    failed += 1;
    test_results["04_encryption_decryption"] = `FAILED: ${err}`;
}

// Test 5: Error Handling
print("\n--- Running Error Handling Tests ---");
try {
    // Clear any existing session
    clear_session();
    
    // Test NoActiveSpace error
    print("Testing NoActiveSpace error...");
    let no_active_space_error_caught = false;
    try {
        // Try to create a keypair without an active space
        create_keypair("test_keypair", "password");
    } catch(err) {
        no_active_space_error_caught = true;
        print(`✓ Caught expected error: ${err}`);
    }
    assert_true(no_active_space_error_caught, "NoActiveSpace error should be caught");
    
    // Create a key space for further tests
    if create_key_space("error_test_space", "password") {
        print(`✓ Key space created successfully`);
        
        // Test KeypairNotFound error
        print("Testing KeypairNotFound error...");
        let keypair_not_found_error_caught = false;
        try {
            // Try to select a non-existent keypair
            select_keypair("nonexistent_keypair");
        } catch(err) {
            keypair_not_found_error_caught = true;
            print(`✓ Caught expected error: ${err}`);
        }
        assert_true(keypair_not_found_error_caught, "KeypairNotFound error should be caught");
        
        // Test NoKeypairSelected error
        print("Testing NoKeypairSelected error...");
        let no_keypair_selected_error_caught = false;
        try {
            // Try to get the public key without selecting a keypair
            keypair_pub_key();
        } catch(err) {
            no_keypair_selected_error_caught = true;
            print(`✓ Caught expected error: ${err}`);
        }
        assert_true(no_keypair_selected_error_caught, "NoKeypairSelected error should be caught");
    }
    
    print("--- Error Handling Tests completed successfully ---");
    passed += 1;
    test_results["05_error_handling"] = "PASSED";
} catch(err) {
    print(`!!! Error in Error Handling Tests: ${err}`);
    failed += 1;
    test_results["05_error_handling"] = `FAILED: ${err}`;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Total: ${passed + failed}`);

// Print detailed results
print("\n=== Detailed Test Results ===");
for key in test_results.keys() {
    let result = test_results[key];
    if result.starts_with("PASSED") {
        print(`✓ ${key}: ${result}`);
    } else {
        print(`✗ ${key}: ${result}`);
    }
}

if failed == 0 {
    print("\n✅ All tests passed!");
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;