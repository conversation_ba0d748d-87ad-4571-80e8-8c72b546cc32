// 02_keyspace_operations.rhai
// Tests for key space operations in the Keypair module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

print("=== Testing Key Space Operations ===");

// Test creating a new key space
print("Testing key space creation...");
let space_name = "test_keyspace";
let password = "secure_password";

if create_key_space(space_name, password) {
    print(`✓ Key space "${space_name}" created successfully`);
    
    // Test adding keypairs to a key space
    print("Testing adding keypairs to key space...");
    let keypair1_name = "keypair1";
    let keypair2_name = "keypair2";
    
    if create_keypair(keypair1_name, password) {
        print(`✓ Keypair "${keypair1_name}" created successfully`);
    } else {
        print(`✗ Failed to create keypair "${keypair1_name}"`);
        throw `Failed to create keypair "${keypair1_name}"`;
    }
    
    if create_keypair(keypair2_name, password) {
        print(`✓ Keypair "${keypair2_name}" created successfully`);
    } else {
        print(`✗ Failed to create keypair "${keypair2_name}"`);
        throw `Failed to create keypair "${keypair2_name}"`;
    }
    
    // Test listing keypairs in a key space
    print("Testing listing keypairs in key space...");
    let keypairs = list_keypairs();
    assert_true(keypairs.len() == 2, `Expected 2 keypairs, got ${keypairs.len()}`);
    assert_true(keypairs.contains(keypair1_name), `Keypair list should contain "${keypair1_name}"`);
    assert_true(keypairs.contains(keypair2_name), `Keypair list should contain "${keypair2_name}"`);
    print(`✓ Listed keypairs successfully: ${keypairs}`);
    
    // Test getting a keypair by name
    print("Testing getting a keypair by name...");
    if select_keypair(keypair1_name) {
        print(`✓ Selected keypair "${keypair1_name}" successfully`);
        let pub_key = keypair_pub_key();
        assert_true(pub_key.len() > 0, "Public key should not be empty");
        print(`✓ Retrieved public key for "${keypair1_name}": ${pub_key.len()} bytes`);
    } else {
        print(`✗ Failed to select keypair "${keypair1_name}"`);
        throw `Failed to select keypair "${keypair1_name}"`;
    }
    
    // Edge case: Attempt to add a keypair with a duplicate name
    print("Testing adding a keypair with a duplicate name...");
    let duplicate_success = false;
    try {
        duplicate_success = create_keypair(keypair1_name, password);
    } catch(err) {
        print(`✓ Caught expected error for duplicate keypair: ${err}`);
    }
    assert_true(!duplicate_success, "Creating a duplicate keypair should fail");
    
    // Edge case: Attempt to get a non-existent keypair
    print("Testing getting a non-existent keypair...");
    let nonexistent_success = false;
    try {
        nonexistent_success = select_keypair("nonexistent_keypair");
    } catch(err) {
        print(`✓ Caught expected error for non-existent keypair: ${err}`);
    }
    assert_true(!nonexistent_success, "Selecting a non-existent keypair should fail");
    
    // Edge case: Test with special characters in keypair names
    print("Testing with special characters in keypair name...");
    let special_name = "special!@#$%^&*()_+";
    if create_keypair(special_name, password) {
        print(`✓ Created keypair with special characters: "${special_name}"`);
        
        // Verify we can select and use it
        if select_keypair(special_name) {
            print(`✓ Selected keypair with special characters`);
            let pub_key = keypair_pub_key();
            assert_true(pub_key.len() > 0, "Public key should not be empty");
        } else {
            print(`✗ Failed to select keypair with special characters`);
            throw `Failed to select keypair with special characters`;
        }
    } else {
        print(`✗ Failed to create keypair with special characters`);
        throw `Failed to create keypair with special characters`;
    }
    
    // Edge case: Test with very long keypair name
    print("Testing with very long keypair name...");
    let long_name = "a" * 100; // 100 character name
    if create_keypair(long_name, password) {
        print(`✓ Created keypair with long name (${long_name.len()} characters)`);
        
        // Verify we can select and use it
        if select_keypair(long_name) {
            print(`✓ Selected keypair with long name`);
            let pub_key = keypair_pub_key();
            assert_true(pub_key.len() > 0, "Public key should not be empty");
        } else {
            print(`✗ Failed to select keypair with long name`);
            throw `Failed to select keypair with long name`;
        }
    } else {
        print(`✗ Failed to create keypair with long name`);
        throw `Failed to create keypair with long name`;
    }
    
    // Edge case: Test with empty keypair name (should fail)
    print("Testing with empty keypair name...");
    let empty_name = "";
    let empty_name_success = false;
    try {
        empty_name_success = create_keypair(empty_name, password);
    } catch(err) {
        print(`✓ Caught expected error for empty keypair name: ${err}`);
    }
    assert_true(!empty_name_success, "Creating a keypair with empty name should fail");
    
    // Stress test: Add multiple keypairs
    print("Stress testing: Adding multiple keypairs...");
    let num_keypairs = 10; // Add 10 more keypairs
    let stress_keypairs = [];
    
    for i in 0..num_keypairs {
        let name = `stress_keypair_${i}`;
        stress_keypairs.push(name);
        
        if create_keypair(name, password) {
            print(`✓ Created stress test keypair ${i+1}/${num_keypairs}`);
        } else {
            print(`✗ Failed to create stress test keypair ${i+1}/${num_keypairs}`);
            throw `Failed to create stress test keypair ${i+1}/${num_keypairs}`;
        }
    }
    
    // Verify all keypairs were created
    print("Verifying all stress test keypairs...");
    let all_keypairs = list_keypairs();
    for name in stress_keypairs {
        assert_true(all_keypairs.contains(name), `Keypair list should contain "${name}"`);
    }
    print(`✓ All ${num_keypairs} stress test keypairs verified`);
    
} else {
    print(`✗ Failed to create key space "${space_name}"`);
    throw `Failed to create key space "${space_name}"`;
}

print("All key space operations tests completed successfully!");