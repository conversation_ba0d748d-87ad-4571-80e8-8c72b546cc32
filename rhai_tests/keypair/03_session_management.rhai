// 03_session_management.rhai
// Tests for session management in the Keypair module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

print("=== Testing Session Management ===");

// Test creating a key space and setting it as current
print("Testing key space creation and activation...");
let space_name1 = "session_test_space1";
let space_name2 = "session_test_space2";
let password = "secure_password";

// Create first key space
if create_key_space(space_name1, password) {
    print(`✓ Key space "${space_name1}" created successfully`);
    
    // Test creating keypairs in the current space
    print("Testing creating keypairs in current space...");
    let keypair1_name = "session_keypair1";
    
    if create_keypair(keypair1_name) {
        print(`✓ Keypair "${keypair1_name}" created successfully in space "${space_name1}"`);
    } else {
        print(`✗ Failed to create keypair "${keypair1_name}" in space "${space_name1}"`);
        throw `Failed to create keypair "${keypair1_name}" in space "${space_name1}"`;
    }
    
    // Test selecting a keypair
    print("Testing selecting a keypair...");
    if select_keypair(keypair1_name) {
        print(`✓ Selected keypair "${keypair1_name}" successfully`);
    } else {
        print(`✗ Failed to select keypair "${keypair1_name}"`);
        throw `Failed to select keypair "${keypair1_name}"`;
    }
    
    // Test getting the selected keypair
    print("Testing getting the selected keypair...");
    let pub_key = keypair_pub_key();
    assert_true(pub_key.len() > 0, "Public key should not be empty");
    print(`✓ Retrieved public key for selected keypair: ${pub_key.len()} bytes`);
    
    // Create second key space
    print("\nTesting creating and switching to a second key space...");
    if create_key_space(space_name2, password) {
        print(`✓ Key space "${space_name2}" created successfully`);
        
        // Verify we're now in the second space
        print("Verifying current space changed...");
        let keypairs = list_keypairs();
        assert_true(keypairs.len() == 0, `Expected 0 keypairs in new space, got ${keypairs.len()}`);
        print("✓ Current space verified as the new space (empty keypair list)");
        
        // Create a keypair in the second space
        let keypair2_name = "session_keypair2";
        if create_keypair(keypair2_name) {
            print(`✓ Keypair "${keypair2_name}" created successfully in space "${space_name2}"`);
        } else {
            print(`✗ Failed to create keypair "${keypair2_name}" in space "${space_name2}"`);
            throw `Failed to create keypair "${keypair2_name}" in space "${space_name2}"`;
        }
        
        // Switch back to first space
        print("\nTesting switching back to first key space...");
        if load_key_space(space_name1, password) {
            print(`✓ Switched back to key space "${space_name1}" successfully`);
            
            // Verify we're now in the first space
            print("Verifying current space changed back...");
            let keypairs = list_keypairs();
            assert_true(keypairs.len() == 1, `Expected 1 keypair in original space, got ${keypairs.len()}`);
            assert_true(keypairs.contains(keypair1_name), `Keypair list should contain "${keypair1_name}"`);
            print("✓ Current space verified as the original space");
        } else {
            print(`✗ Failed to switch back to key space "${space_name1}"`);
            throw `Failed to switch back to key space "${space_name1}"`;
        }
    } else {
        print(`✗ Failed to create second key space "${space_name2}"`);
        throw `Failed to create second key space "${space_name2}"`;
    }
    
    // Test clearing the session
    print("\nTesting clearing the session...");
    clear_session();
    print("✓ Session cleared");
    
    // Verify operations fail after clearing session
    print("Verifying operations fail after clearing session...");
    let list_success = false;
    try {
        list_keypairs();
        list_success = true;
    } catch(err) {
        print(`✓ Caught expected error after clearing session: ${err}`);
    }
    assert_true(!list_success, "Listing keypairs should fail after clearing session");
    
    // Error case: Attempt operations without an active key space
    print("\nTesting operations without an active key space...");
    
    // Attempt to create a keypair
    let create_success = false;
    try {
        create_success = create_keypair("test_keypair_2");
    } catch(err) {
        print(`✓ Caught expected error for creating keypair without active space: ${err}`);
    }
    assert_true(!create_success, "Creating a keypair without active space should fail");
    
    // Attempt to select a keypair
    let select_success = false;
    try {
        select_success = select_keypair("no_space_keypair");
    } catch(err) {
        print(`✓ Caught expected error for selecting keypair without active space: ${err}`);
    }
    assert_true(!select_success, "Selecting a keypair without active space should fail");
    
    // Reload a key space
    print("\nTesting reloading a key space after clearing session...");
    if load_key_space(space_name1, password) {
        print(`✓ Reloaded key space "${space_name1}" successfully`);
        
        // Verify the keypair is still there
        let keypairs = list_keypairs();
        assert_true(keypairs.contains(keypair1_name), `Keypair list should contain "${keypair1_name}"`);
        print("✓ Keypair still exists in reloaded space");
    } else {
        print(`✗ Failed to reload key space "${space_name1}"`);
        throw `Failed to reload key space "${space_name1}"`;
    }
    
    // Error case: Attempt to get selected keypair when none is selected
    print("\nTesting getting selected keypair when none is selected...");
    let get_selected_success = false;
    try {
        keypair_pub_key();
        get_selected_success = true;
    } catch(err) {
        print(`✓ Caught expected error for getting selected keypair when none selected: ${err}`);
    }
    assert_true(!get_selected_success, "Getting selected keypair when none is selected should fail");
    
    // Error case: Attempt to select non-existent keypair
    print("\nTesting selecting a non-existent keypair...");
    let select_nonexistent_success = false;
    try {
        select_nonexistent_success = select_keypair("nonexistent_keypair");
    } catch(err) {
        print(`✓ Caught expected error for selecting non-existent keypair: ${err}`);
    }
    assert_true(!select_nonexistent_success, "Selecting a non-existent keypair should fail");
    
} else {
    print(`✗ Failed to create key space "${space_name1}"`);
    throw `Failed to create key space "${space_name1}"`;
}

print("All session management tests completed successfully!");