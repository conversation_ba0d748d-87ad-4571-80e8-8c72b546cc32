// 01_keypair_operations.rhai
// Tests for basic keypair operations in the Keypair module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

print("=== Testing Basic Keypair Operations ===");

// Test creating a new keypair
print("Testing keypair creation...");
let keypair_name = "test_keypair";
if create_key_space("test_space", "password") {
    print("✓ Key space created successfully");
    
    if create_keypair(keypair_name, "password") {
        print("✓ Keypair created successfully");
        
        // Test getting the public key
        print("Testing public key retrieval...");
        if select_keypair(keypair_name) {
            let pub_key = keypair_pub_key();
            assert_true(pub_key.len() > 0, "Public key should not be empty");
            print(`✓ Public key retrieved: ${pub_key.len()} bytes`);
            
            // Test signing a message
            print("Testing message signing...");
            let message = "This is a test message to sign";
            let signature = keypair_sign(message);
            assert_true(signature.len() > 0, "Signature should not be empty");
            print(`✓ Message signed successfully: ${signature.len()} bytes`);
            
            // Test verifying a signature
            print("Testing signature verification...");
            let is_valid = keypair_verify(message, signature);
            assert_true(is_valid, "Signature should be valid");
            print("✓ Signature verified successfully");
            
            // Test verifying with just a public key
            print("Testing verification with public key only...");
            let is_valid_pub = verify_with_public_key(pub_key, message, signature);
            assert_true(is_valid_pub, "Signature should be valid with public key only");
            print("✓ Signature verified with public key only");
            
            // Edge case: Empty message
            print("Testing with empty message...");
            let empty_message = "";
            let empty_signature = keypair_sign(empty_message);
            assert_true(empty_signature.len() > 0, "Signature for empty message should not be empty");
            let is_valid_empty = keypair_verify(empty_message, empty_signature);
            assert_true(is_valid_empty, "Empty message signature should be valid");
            print("✓ Empty message signed and verified successfully");
            
            // Edge case: Large message
            print("Testing with large message...");
            let large_message = "A" * 10000; // 10KB message
            let large_signature = keypair_sign(large_message);
            assert_true(large_signature.len() > 0, "Signature for large message should not be empty");
            let is_valid_large = keypair_verify(large_message, large_signature);
            assert_true(is_valid_large, "Large message signature should be valid");
            print("✓ Large message signed and verified successfully");
            
            // Error case: Invalid signature format
            print("Testing with invalid signature format...");
            let invalid_signature = [0, 1, 2, 3]; // Invalid signature bytes
            let is_valid_invalid = false;
            try {
                is_valid_invalid = keypair_verify(message, invalid_signature);
            } catch(err) {
                print(`✓ Caught expected error for invalid signature: ${err}`);
            }
            assert_true(!is_valid_invalid, "Invalid signature should not verify");
            
            // Error case: Tampered message
            print("Testing with tampered message...");
            let tampered_message = message + " (tampered)";
            let is_valid_tampered = keypair_verify(tampered_message, signature);
            assert_true(!is_valid_tampered, "Tampered message should not verify");
            print("✓ Tampered message correctly failed verification");
            
            // Error case: Malformed public key
            print("Testing with malformed public key...");
            let malformed_pub_key = [0, 1, 2, 3]; // Invalid public key bytes
            let is_valid_malformed = false;
            try {
                is_valid_malformed = verify_with_public_key(malformed_pub_key, message, signature);
            } catch(err) {
                print(`✓ Caught expected error for malformed public key: ${err}`);
            }
            assert_true(!is_valid_malformed, "Malformed public key should not verify");
        } else {
            print("✗ Failed to select keypair");
            throw "Failed to select keypair";
        }
    } else {
        print("✗ Failed to create keypair");
        throw "Failed to create keypair";
    }
} else {
    print("✗ Failed to create key space");
    throw "Failed to create key space";
}

print("All keypair operations tests completed successfully!");