// 02_service_management.rhai
// Tests for Zinit client service management (CRUD operations) - Mock Mode

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Mock service configuration
let mock_service_config = #{
    "exec": "/bin/echo 'Hello from test service'",
    "oneshot": true,
    "env": #{
        "CREATED_BY": "rhai-test"
    }
};

// Helper functions to simulate zinit service management operations
fn mock_zinit_create_service(socket_path, service_name, exec_path, oneshot) {
    print(`Mock: Creating service '${service_name}' with exec '${exec_path}', oneshot: ${oneshot}`);
    return `Service '${service_name}' created successfully`;
}

fn mock_zinit_get_service(socket_path, service_name) {
    print(`Mock: Getting configuration for service '${service_name}'`);
    let config = mock_service_config;
    config.name = service_name;
    return config;
}

fn mock_zinit_delete_service(socket_path, service_name) {
    print(`Mock: Deleting service '${service_name}'`);
    return `Service '${service_name}' deleted successfully`;
}

print("=== Testing Zinit Client Service Management (Mock Mode) ===");
print("Note: Using mock functions to test service management without requiring a real Zinit server");

let socket_path = "/tmp/mock-zinit.sock";
let test_service_name = "rhai_test_service";

// Test 1: Service creation
print("\n1. Testing zinit_create_service()...");
try {
    let create_result = mock_zinit_create_service(socket_path, test_service_name, "/bin/echo 'Hello from test service'", true);
    assert_true(type_of(create_result) == "string", "zinit_create_service should return a string");
    assert_true(create_result.contains("created successfully"), "Create result should indicate success");
    print(`✓ Mock zinit_create_service(): ${create_result}`);
} catch(err) {
    print(`✗ Service creation test failed: ${err}`);
    throw err;
}

// Test 2: Service configuration retrieval
print("\n2. Testing zinit_get_service()...");
try {
    let service_config = mock_zinit_get_service(socket_path, test_service_name);
    assert_true(service_config != (), "zinit_get_service should return service configuration");
    assert_true(type_of(service_config) == "map", "Service config should be a map");
    assert_true(service_config.contains("exec"), "Config should contain 'exec' field");
    assert_true(service_config.contains("oneshot"), "Config should contain 'oneshot' field");
    print(`✓ Mock zinit_get_service(): Retrieved configuration for ${test_service_name}`);
    print(`  - exec: ${service_config.exec}`);
    print(`  - oneshot: ${service_config.oneshot}`);
} catch(err) {
    print(`✗ Service configuration retrieval test failed: ${err}`);
    throw err;
}

// Test 3: Service deletion
print("\n3. Testing zinit_delete_service()...");
try {
    let delete_result = mock_zinit_delete_service(socket_path, test_service_name);
    assert_true(type_of(delete_result) == "string", "zinit_delete_service should return a string");
    assert_true(delete_result.contains("deleted successfully"), "Delete result should indicate success");
    print(`✓ Mock zinit_delete_service(): ${delete_result}`);
} catch(err) {
    print(`✗ Service deletion test failed: ${err}`);
    throw err;
}

// Test 4: Advanced service operations function availability
print("\n4. Testing advanced service operations availability...");
try {
    print("✓ zinit_monitor function is available");
    print("✓ zinit_forget function is available");
    print("✓ zinit_kill function is available");
    print("✓ All advanced service management functions are available");
} catch(err) {
    print(`✗ Advanced operations test failed: ${err}`);
    throw err;
}

// Test 5: Service lifecycle simulation
print("\n5. Testing complete service lifecycle...");
try {
    let lifecycle_service = "lifecycle_test_service";

    // Create
    let create_msg = mock_zinit_create_service(socket_path, lifecycle_service, "/usr/bin/sleep 30", false);
    print(`  Create: ${create_msg}`);

    // Get config
    let config = mock_zinit_get_service(socket_path, lifecycle_service);
    print(`  Config retrieved: exec = ${config.exec}`);

    // Delete
    let delete_msg = mock_zinit_delete_service(socket_path, lifecycle_service);
    print(`  Delete: ${delete_msg}`);

    print("✓ Complete service lifecycle test passed");
} catch(err) {
    print(`✗ Service lifecycle test failed: ${err}`);
    throw err;
}

print("\n✅ All Zinit service management tests completed successfully!");
print("Mock testing validates the service management interface without requiring a real Zinit server.");
