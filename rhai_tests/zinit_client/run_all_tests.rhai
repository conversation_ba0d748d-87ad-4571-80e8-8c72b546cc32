// run_all_tests.rhai
// Runs all Zinit client module tests

print("=== Running Zinit Client Module Tests ===");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if Zinit is available
fn is_zinit_available() {
    try {
        // Try to list services to check if Zinit is available
        let socket_path = "/tmp/zinit.sock";
        let services = zinit_list(socket_path);
        return true;
    } catch(err) {
        print(`Zinit connection error: ${err}`);
        return false;
    }
}

// Run each test directly
let passed = 0;
let failed = 0;
let skipped = 0;

// Check if Zinit is available
let zinit_available = is_zinit_available();
if !zinit_available {
    print("Zinit server is not available. Skipping all Zinit tests.");
    print("Note: These tests require a running Zinit daemon with socket at /tmp/zinit.sock");
    print("To start Zinit: sudo zinit --socket /tmp/zinit.sock init");
    skipped = 3; // Skip all three tests
} else {
    let socket_path = "/tmp/zinit.sock";

    // Test 1: Service Operations
    print("\n--- Running Zinit Service Operations Tests ---");
    try {
        // Test zinit_list function
        print("Testing zinit_list()...");
        let services = zinit_list(socket_path);
        assert_true(type_of(services) == "map", "zinit_list should return a map");
        print(`✓ zinit_list(): Found ${services.len()} services`);

        // Create a test service for status testing
        let test_service_name = "rhai_test_ops";
        print("Creating test service for operations...");
        let create_result = zinit_create_service(socket_path, test_service_name, "/bin/echo 'Test service'", true);
        print(`✓ Test service created: ${create_result}`);

        // Test zinit_status function
        print("Testing zinit_status()...");
        let status = zinit_status(socket_path, test_service_name);
        assert_true(type_of(status) == "map", "zinit_status should return a map");
        assert_true(status.contains("name"), "Status should contain 'name' field");
        print(`✓ zinit_status(): Service ${test_service_name} has PID ${status.pid}`);

        // Clean up test service
        let delete_result = zinit_delete_service(socket_path, test_service_name);
        print(`✓ Test service cleaned up: ${delete_result}`);

        print("--- Zinit Service Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Zinit Service Operations Tests: ${err}`);
        failed += 1;
    }

    // Test 2: Service Management
    print("\n--- Running Zinit Service Management Tests ---");
    try {
        let test_service_name = "rhai_test_management";

        // Test service creation
        print("Testing zinit_create_service()...");
        let create_result = zinit_create_service(socket_path, test_service_name, "/bin/sleep 5", false);
        assert_true(type_of(create_result) == "string", "zinit_create_service should return a string");
        print(`✓ zinit_create_service(): ${create_result}`);

        // Test service configuration retrieval
        print("Testing zinit_get_service()...");
        let service_config = zinit_get_service(socket_path, test_service_name);
        assert_true(service_config != (), "zinit_get_service should return service configuration");
        print(`✓ zinit_get_service(): Retrieved configuration for ${test_service_name}`);

        // Test service control
        print("Testing service control operations...");
        let start_result = zinit_start(socket_path, test_service_name);
        print(`✓ Service started`);

        sleep(2000); // Wait 2 seconds

        let stop_result = zinit_stop(socket_path, test_service_name);
        print(`✓ Service stopped`);

        // Test service deletion
        print("Testing zinit_delete_service()...");
        let delete_result = zinit_delete_service(socket_path, test_service_name);
        assert_true(type_of(delete_result) == "string", "zinit_delete_service should return a string");
        print(`✓ zinit_delete_service(): ${delete_result}`);

        print("--- Zinit Service Management Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Zinit Service Management Tests: ${err}`);
        failed += 1;
    }

    // Test 3: Logs Operations
    print("\n--- Running Zinit Logs Operations Tests ---");
    try {
        // Test zinit_logs_all function
        print("Testing zinit_logs_all()...");
        let all_logs = zinit_logs_all(socket_path);
        assert_true(type_of(all_logs) == "array", "zinit_logs_all should return an array");
        print(`✓ zinit_logs_all(): Retrieved ${all_logs.len()} log entries`);

        // Create a test service to generate logs
        let log_test_service = "rhai_test_logs";
        print("Creating test service for logs...");
        let create_result = zinit_create_service(socket_path, log_test_service, "/bin/echo 'Log test message'", true);
        print(`✓ Log test service created`);

        // Start the service to generate logs
        let start_result = zinit_start(socket_path, log_test_service);
        print(`✓ Log test service started`);

        sleep(2000); // Wait for service to complete

        // Test zinit_logs function with filter
        print("Testing zinit_logs() with filter...");
        let service_logs = zinit_logs(socket_path, log_test_service);
        assert_true(type_of(service_logs) == "array", "zinit_logs should return an array");
        print(`✓ zinit_logs(): Retrieved ${service_logs.len()} log entries for ${log_test_service}`);

        // Clean up log test service
        let delete_result = zinit_delete_service(socket_path, log_test_service);
        print(`✓ Log test service cleaned up`);

        print("Note: Current implementation returns empty logs as placeholder");
        print("--- Zinit Logs Operations Tests completed successfully ---");
        passed += 1;
    } catch(err) {
        print(`!!! Error in Zinit Logs Operations Tests: ${err}`);
        failed += 1;
    }
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Skipped: ${skipped}`);
print(`Total: ${passed + failed + skipped}`);

if failed == 0 {
    if skipped > 0 {
        print("\n⚠️ All tests skipped or passed!");
        print("To run Zinit tests, ensure Zinit daemon is running with socket at /tmp/zinit.sock");
        print("Start with: sudo zinit --socket /tmp/zinit.sock init");
    } else {
        print("\n✅ All tests passed!");
        print("Real Zinit server testing validates actual functionality.");
    }
} else {
    print("\n❌ Some tests failed!");
}

// Return the number of failed tests (0 means success)
failed;
