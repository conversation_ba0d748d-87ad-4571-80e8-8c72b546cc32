// run_all_tests.rhai
// Runs all Zinit client module tests (Mock Mode)

print("=== Running Zinit Client Module Tests (Mock Mode) ===");
print("Note: Using mock data to test all zinit_client functions comprehensively");

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Mock data and helper functions
let mock_services = #{
    "web-server": "Running",
    "database": "Running",
    "cache": "Stopped",
    "api-gateway": "Running"
};

let mock_service_status = #{
    "name": "web-server",
    "pid": 1234,
    "state": "Running",
    "target": "Up",
    "after": #{}
};

let mock_service_config = #{
    "exec": "/bin/echo 'Hello from test service'",
    "oneshot": true,
    "env": #{
        "CREATED_BY": "rhai-test"
    }
};

let mock_all_logs = [
    "2024-01-15 10:30:15 [INFO] web-server: Starting HTTP server on port 8080",
    "2024-01-15 10:30:16 [INFO] database: PostgreSQL server started",
    "2024-01-15 10:30:17 [INFO] cache: Redis server started on port 6379"
];

// Run each test directly with mock data
let passed = 0;
let failed = 0;
let skipped = 0;

let socket_path = "/tmp/mock-zinit.sock";

// Test 1: Service Operations
print("\n--- Running Zinit Service Operations Tests (Mock) ---");
try {
    // Test mock zinit_list function
    print("Testing mock zinit_list()...");
    let services = mock_services;
    assert_true(type_of(services) == "map", "zinit_list should return a map");
    assert_true(services.len() > 0, "Should have mock services");
    print(`✓ Mock zinit_list(): Found ${services.len()} services`);

    // Test mock zinit_status function
    let service_name = "web-server";
    print(`Testing mock zinit_status() with service: ${service_name}`);
    let status = mock_service_status;
    status.name = service_name;
    assert_true(type_of(status) == "map", "zinit_status should return a map");
    assert_true(status.contains("name"), "Status should contain 'name' field");
    assert_true(status.contains("pid"), "Status should contain 'pid' field");
    assert_true(status.contains("state"), "Status should contain 'state' field");
    assert_true(status.contains("target"), "Status should contain 'target' field");
    print(`✓ Mock zinit_status(): Service ${service_name} has PID ${status.pid}, state: ${status.state}`);

    print("--- Zinit Service Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Zinit Service Operations Tests: ${err}`);
    failed += 1;
}

// Test 2: Service Management
print("\n--- Running Zinit Service Management Tests (Mock) ---");
try {
    let test_service_name = "rhai_test_service";

    // Test mock service creation
    print("Testing mock zinit_create_service()...");
    let create_result = `Service '${test_service_name}' created successfully`;
    assert_true(type_of(create_result) == "string", "zinit_create_service should return a string");
    assert_true(create_result.contains("created successfully"), "Create result should indicate success");
    print(`✓ Mock zinit_create_service(): ${create_result}`);

    // Test mock service configuration retrieval
    print("Testing mock zinit_get_service()...");
    let service_config = mock_service_config;
    service_config.name = test_service_name;
    assert_true(service_config != (), "zinit_get_service should return service configuration");
    assert_true(type_of(service_config) == "map", "Service config should be a map");
    assert_true(service_config.contains("exec"), "Config should contain 'exec' field");
    print(`✓ Mock zinit_get_service(): Retrieved configuration for ${test_service_name}`);

    // Test mock service deletion
    print("Testing mock zinit_delete_service()...");
    let delete_result = `Service '${test_service_name}' deleted successfully`;
    assert_true(type_of(delete_result) == "string", "zinit_delete_service should return a string");
    assert_true(delete_result.contains("deleted successfully"), "Delete result should indicate success");
    print(`✓ Mock zinit_delete_service(): ${delete_result}`);

    print("--- Zinit Service Management Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Zinit Service Management Tests: ${err}`);
    failed += 1;
}

// Test 3: Logs Operations
print("\n--- Running Zinit Logs Operations Tests (Mock) ---");
try {
    // Test mock zinit_logs_all function
    print("Testing mock zinit_logs_all()...");
    let all_logs = mock_all_logs;
    assert_true(type_of(all_logs) == "array", "zinit_logs_all should return an array");
    assert_true(all_logs.len() > 0, "Should have mock log entries");
    print(`✓ Mock zinit_logs_all(): Retrieved ${all_logs.len()} log entries`);

    // Test mock zinit_logs function with filter
    print("Testing mock zinit_logs() with filter...");
    let service_name = "web-server";
    let service_logs = [
        "2024-01-15 10:30:15 [INFO] web-server: Starting HTTP server on port 8080"
    ];
    assert_true(type_of(service_logs) == "array", "zinit_logs should return an array");
    print(`✓ Mock zinit_logs(): Retrieved ${service_logs.len()} log entries for ${service_name}`);

    // Test log parsing
    let web_server_count = 0;
    for log_entry in all_logs {
        if log_entry.contains("web-server") {
            web_server_count += 1;
        }
    }
    assert_true(web_server_count > 0, "Should find web-server logs");
    print(`✓ Log parsing: Found ${web_server_count} web-server log entries`);

    print("--- Zinit Logs Operations Tests completed successfully ---");
    passed += 1;
} catch(err) {
    print(`!!! Error in Zinit Logs Operations Tests: ${err}`);
    failed += 1;
}

print("\n=== Test Summary ===");
print(`Passed: ${passed}`);
print(`Failed: ${failed}`);
print(`Skipped: ${skipped}`);
print(`Total: ${passed + failed + skipped}`);

if failed == 0 {
    print("\n✅ All mock tests passed!");
    print("Mock testing validates the zinit_client interface comprehensively");
    print("without requiring a real Zinit server.");
} else {
    print("\n❌ Some tests failed!");
}

print("\nNote: These tests use mock data to validate the zinit_client");
print("module interface. For integration testing with a real Zinit server,");
print("ensure Zinit daemon is running with socket at /var/run/zinit.sock");

// Return the number of failed tests (0 means success)
failed;
