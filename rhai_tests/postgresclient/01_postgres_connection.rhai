// 01_postgres_connection.rhai
// Tests for PostgreSQL client connection and basic operations

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Helper function to check if PostgreSQL is available
fn is_postgres_available() {
    try {
        // Try to execute a simple connection
        let connect_result = pg_connect();
        return connect_result;
    } catch(err) {
        print(`PostgreSQL connection error: ${err}`);
        return false;
    }
}

print("=== Testing PostgreSQL Client Connection ===");

// Check if PostgreSQL is available
let postgres_available = is_postgres_available();
if !postgres_available {
    print("PostgreSQL server is not available. Skipping PostgreSQL tests.");
    // Exit gracefully without error
    return;
}

print("✓ PostgreSQL server is available");

// Test pg_ping function
print("Testing pg_ping()...");
let ping_result = pg_ping();
assert_true(ping_result, "PING should return true");
print(`✓ pg_ping(): Returned ${ping_result}`);

// Test pg_execute function
print("Testing pg_execute()...");
let test_table = "rhai_test_table";

// Create a test table
let create_table_query = `
    CREATE TABLE IF NOT EXISTS ${test_table} (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        value INTEGER
    )
`;

let create_result = pg_execute(create_table_query);
assert_true(create_result >= 0, "CREATE TABLE operation should succeed");
print(`✓ pg_execute(): Successfully created table ${test_table}`);

// Insert a test row
let insert_query = `
    INSERT INTO ${test_table} (name, value)
    VALUES ('test_name', 42)
`;

let insert_result = pg_execute(insert_query);
assert_true(insert_result > 0, "INSERT operation should succeed");
print(`✓ pg_execute(): Successfully inserted row into ${test_table}`);

// Test pg_query function
print("Testing pg_query()...");
let select_query = `
    SELECT * FROM ${test_table}
`;

let select_result = pg_query(select_query);
assert_true(select_result.len() > 0, "SELECT should return at least one row");
print(`✓ pg_query(): Successfully retrieved ${select_result.len()} rows from ${test_table}`);

// Test pg_query_one function
print("Testing pg_query_one()...");
let select_one_query = `
    SELECT * FROM ${test_table} LIMIT 1
`;

let select_one_result = pg_query_one(select_one_query);
assert_true(select_one_result["name"] == "test_name", "SELECT ONE should return the correct name");
assert_true(select_one_result["value"] == "42", "SELECT ONE should return the correct value");
print(`✓ pg_query_one(): Successfully retrieved row with name=${select_one_result["name"]} and value=${select_one_result["value"]}`);

// Clean up
print("Cleaning up...");
let drop_table_query = `
    DROP TABLE IF EXISTS ${test_table}
`;

let drop_result = pg_execute(drop_table_query);
assert_true(drop_result >= 0, "DROP TABLE operation should succeed");
print(`✓ pg_execute(): Successfully dropped table ${test_table}`);

// Test pg_reset function
print("Testing pg_reset()...");
let reset_result = pg_reset();
assert_true(reset_result, "RESET should return true");
print(`✓ pg_reset(): Successfully reset PostgreSQL client`);

print("All PostgreSQL connection tests completed successfully!");
