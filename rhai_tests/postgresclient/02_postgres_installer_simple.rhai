// PostgreSQL Installer Test (Simplified)
//
// This test script demonstrates how to use the PostgreSQL installer module to:
// - Install PostgreSQL using nerdctl
// - Create a database
// - Execute SQL scripts
// - Check if PostgreSQL is running

// Define test variables
let container_name = "postgres-test";
let postgres_version = "15";
let postgres_port = 5433;  // Use a non-default port to avoid conflicts
let postgres_user = "testuser";
let postgres_password = "testpassword";
let test_db_name = "testdb";

// Main test function
fn test_postgres_installer() {
    print("\n=== PostgreSQL Installer Test ===");
    
    // Test 1: Install PostgreSQL
    print("\n1. Installing PostgreSQL...");
    try {
        let install_result = pg_install(
            container_name,
            postgres_version,
            postgres_port,
            postgres_user,
            postgres_password
        );
        
        print(`PostgreSQL installation result: ${install_result}`);
        print("✓ PostgreSQL installed successfully");
    } catch(e) {
        print(`✗ Failed to install PostgreSQL: ${e}`);
        return;
    }
    
    // Test 2: Check if PostgreSQL is running
    print("\n2. Checking if PostgreSQL is running...");
    try {
        let running = pg_is_running(container_name);
        print(`PostgreSQL running status: ${running}`);
        print("✓ PostgreSQL is running");
    } catch(e) {
        print(`✗ Failed to check if PostgreSQL is running: ${e}`);
        return;
    }
    
    // Test 3: Create a database
    print("\n3. Creating a database...");
    try {
        let create_result = pg_create_database(container_name, test_db_name);
        print(`Database creation result: ${create_result}`);
        print(`✓ Database '${test_db_name}' created successfully`);
    } catch(e) {
        print(`✗ Failed to create database: ${e}`);
        return;
    }
    
    // Test 4: Execute SQL script
    print("\n4. Executing SQL script...");
    try {
        // Create a table
        let create_table_sql = `
            CREATE TABLE test_table (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                value INTEGER
            );
        `;
        
        let result = pg_execute_sql(container_name, test_db_name, create_table_sql);
        print("✓ Created table successfully");
        
        // Insert data
        let insert_sql = `
            INSERT INTO test_table (name, value) VALUES 
            ('test1', 100),
            ('test2', 200),
            ('test3', 300);
        `;
        
        result = pg_execute_sql(container_name, test_db_name, insert_sql);
        print("✓ Inserted data successfully");
        
        // Query data
        let query_sql = "SELECT * FROM test_table ORDER BY id;";
        result = pg_execute_sql(container_name, test_db_name, query_sql);
        print("✓ Queried data successfully");
        print(`Query result: ${result}`);
    } catch(e) {
        print(`✗ Failed to execute SQL script: ${e}`);
        return;
    }
    
    print("\n=== PostgreSQL Installer Test Completed Successfully ===");
}

// Run the test
test_postgres_installer();
