// PostgreSQL Installer Test
//
// This test script demonstrates how to use the PostgreSQL installer module to:
// - Install PostgreSQL using nerdctl
// - Create a database
// - Execute SQL scripts
// - Check if PostgreSQL is running
//
// Prerequisites:
// - nerdctl must be installed and working
// - Docker images must be accessible

// Define utility functions
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Define test variables (will be used inside the test function)

// Function to check if nerdctl is available
fn is_nerdctl_available() {
    try {
        // For testing purposes, we'll assume nerdctl is not available
        // In a real-world scenario, you would check if nerdctl is installed
        return false;
    } catch {
        return false;
    }
}

// Function to clean up any existing PostgreSQL container
fn cleanup_postgres() {
    try {
        // In a real-world scenario, you would use nerdctl to stop and remove the container
        // For this test, we'll just print a message
        print("Cleaned up existing PostgreSQL container (simulated)");
    } catch {
        // Ignore errors if container doesn't exist
    }
}

// Main test function
fn run_postgres_installer_test() {
    print("\n=== PostgreSQL Installer Test ===");

    // Define test variables
    let container_name = "postgres-test";
    let postgres_version = "15";
    let postgres_port = 5433;  // Use a non-default port to avoid conflicts
    let postgres_user = "testuser";
    let postgres_password = "testpassword";
    let test_db_name = "testdb";

    // // Check if nerdctl is available
    // if !is_nerdctl_available() {
    //     print("nerdctl is not available. Skipping PostgreSQL installer test.");
    //     return 1;  // Skip the test
    // }

    // Clean up any existing PostgreSQL container
    cleanup_postgres();

    // Test 1: Install PostgreSQL
    print("\n1. Installing PostgreSQL...");
    try {
        let install_result = pg_install(
            container_name,
            postgres_version,
            postgres_port,
            postgres_user,
            postgres_password
        );

        assert_true(install_result, "PostgreSQL installation should succeed");
        print("✓ PostgreSQL installed successfully");

        // Wait a bit for PostgreSQL to fully initialize
        print("Waiting for PostgreSQL to initialize...");
        // In a real-world scenario, you would wait for PostgreSQL to initialize
        // For this test, we'll just print a message
        print("Waited for PostgreSQL to initialize (simulated)")
    } catch(e) {
        print(`✗ Failed to install PostgreSQL: ${e}`);
        cleanup_postgres();
        return 1;  // Test failed
    }

    // Test 2: Check if PostgreSQL is running
    print("\n2. Checking if PostgreSQL is running...");
    try {
        let running = pg_is_running(container_name);
        assert_true(running, "PostgreSQL should be running");
        print("✓ PostgreSQL is running");
    } catch(e) {
        print(`✗ Failed to check if PostgreSQL is running: ${e}`);
        cleanup_postgres();
        return 1;  // Test failed
    }

    // Test 3: Create a database
    print("\n3. Creating a database...");
    try {
        let create_result = pg_create_database(container_name, test_db_name);
        assert_true(create_result, "Database creation should succeed");
        print(`✓ Database '${test_db_name}' created successfully`);
    } catch(e) {
        print(`✗ Failed to create database: ${e}`);
        cleanup_postgres();
        return 1;  // Test failed
    }

    // Test 4: Execute SQL script
    print("\n4. Executing SQL script...");
    try {
        // Create a table
        let create_table_sql = `
            CREATE TABLE test_table (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                value INTEGER
            );
        `;

        let result = pg_execute_sql(container_name, test_db_name, create_table_sql);
        print("✓ Created table successfully");

        // Insert data
        let insert_sql = `
            INSERT INTO test_table (name, value) VALUES
            ('test1', 100),
            ('test2', 200),
            ('test3', 300);
        `;

        result = pg_execute_sql(container_name, test_db_name, insert_sql);
        print("✓ Inserted data successfully");

        // Query data
        let query_sql = "SELECT * FROM test_table ORDER BY id;";
        result = pg_execute_sql(container_name, test_db_name, query_sql);
        print("✓ Queried data successfully");
        print(`Query result: ${result}`);
    } catch(e) {
        print(`✗ Failed to execute SQL script: ${e}`);
        cleanup_postgres();
        return 1;  // Test failed
    }

    // Clean up
    print("\nCleaning up...");
    cleanup_postgres();

    print("\n=== PostgreSQL Installer Test Completed Successfully ===");
    return 0;  // Test passed
}

// Run the test
let result = run_postgres_installer_test();

// Return the result
result
