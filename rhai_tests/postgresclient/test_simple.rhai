// Simple test script to verify that the Rhai engine is working

print("Hello, world!");

// Try to access the PostgreSQL installer functions
print("\nTrying to access PostgreSQL installer functions...");

// Try to call the pg_install function
try {
    let result = pg_install(
        "postgres-test",
        "15",
        5433,
        "testuser",
        "testpassword"
    );
    print("pg_install result: " + result);
} catch(e) {
    print("Error calling pg_install: " + e);
}

print("\nTest completed!");
