// Test script to check if the PostgreSQL functions are registered

// Try to call the basic PostgreSQL functions
try {
    print("Trying to call pg_connect()...");
    let result = pg_connect();
    print("pg_connect result: " + result);
} catch(e) {
    print("<PERSON>rror calling pg_connect: " + e);
}

// Try to call the pg_ping function
try {
    print("\nTrying to call pg_ping()...");
    let result = pg_ping();
    print("pg_ping result: " + result);
} catch(e) {
    print("Error calling pg_ping: " + e);
}

// Try to call the pg_reset function
try {
    print("\nTrying to call pg_reset()...");
    let result = pg_reset();
    print("pg_reset result: " + result);
} catch(e) {
    print("Error calling pg_reset: " + e);
}

// Try to call the pg_execute function
try {
    print("\nTrying to call pg_execute()...");
    let result = pg_execute("SELECT 1");
    print("pg_execute result: " + result);
} catch(e) {
    print("<PERSON>rror calling pg_execute: " + e);
}

// Try to call the pg_query function
try {
    print("\nTrying to call pg_query()...");
    let result = pg_query("SELECT 1");
    print("pg_query result: " + result);
} catch(e) {
    print("Error calling pg_query: " + e);
}

// Try to call the pg_query_one function
try {
    print("\nTrying to call pg_query_one()...");
    let result = pg_query_one("SELECT 1");
    print("pg_query_one result: " + result);
} catch(e) {
    print("Error calling pg_query_one: " + e);
}

// Try to call the pg_install function
try {
    print("\nTrying to call pg_install()...");
    let result = pg_install("postgres-test", "15", 5433, "testuser", "testpassword");
    print("pg_install result: " + result);
} catch(e) {
    print("Error calling pg_install: " + e);
}

// Try to call the pg_create_database function
try {
    print("\nTrying to call pg_create_database()...");
    let result = pg_create_database("postgres-test", "testdb");
    print("pg_create_database result: " + result);
} catch(e) {
    print("Error calling pg_create_database: " + e);
}

// Try to call the pg_execute_sql function
try {
    print("\nTrying to call pg_execute_sql()...");
    let result = pg_execute_sql("postgres-test", "testdb", "SELECT 1");
    print("pg_execute_sql result: " + result);
} catch(e) {
    print("Error calling pg_execute_sql: " + e);
}

// Try to call the pg_is_running function
try {
    print("\nTrying to call pg_is_running()...");
    let result = pg_is_running("postgres-test");
    print("pg_is_running result: " + result);
} catch(e) {
    print("Error calling pg_is_running: " + e);
}

print("\nTest completed!");
