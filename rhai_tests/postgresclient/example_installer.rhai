// PostgreSQL Installer Example
//
// This example demonstrates how to use the PostgreSQL installer module to:
// - Install PostgreSQL using nerdctl
// - Create a database
// - Execute SQL scripts
// - Check if PostgreSQL is running
//
// Prerequisites:
// - nerdctl must be installed and working
// - Docker images must be accessible

// Define variables
let container_name = "postgres-example";
let postgres_version = "15";
let postgres_port = 5432;
let postgres_user = "exampleuser";
let postgres_password = "examplepassword";
let db_name = "exampledb";

// Install PostgreSQL
print("Installing PostgreSQL...");
try {
    let install_result = pg_install(
        container_name,
        postgres_version,
        postgres_port,
        postgres_user,
        postgres_password
    );
    
    print("PostgreSQL installed successfully!");
    
    // Check if PostgreSQL is running
    print("\nChecking if PostgreSQL is running...");
    let running = pg_is_running(container_name);
    
    if (running) {
        print("PostgreSQL is running!");
        
        // Create a database
        print("\nCreating a database...");
        let create_result = pg_create_database(container_name, db_name);
        print(`Database '${db_name}' created successfully!`);
        
        // Create a table
        print("\nCreating a table...");
        let create_table_sql = `
            CREATE TABLE users (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL
            );
        `;
        
        let result = pg_execute_sql(container_name, db_name, create_table_sql);
        print("Table created successfully!");
        
        // Insert data
        print("\nInserting data...");
        let insert_sql = `
            INSERT INTO users (name, email) VALUES 
            ('John Doe', '<EMAIL>'),
            ('Jane Smith', '<EMAIL>');
        `;
        
        result = pg_execute_sql(container_name, db_name, insert_sql);
        print("Data inserted successfully!");
        
        // Query data
        print("\nQuerying data...");
        let query_sql = "SELECT * FROM users;";
        result = pg_execute_sql(container_name, db_name, query_sql);
        print(`Query result: ${result}`);
    } else {
        print("PostgreSQL is not running!");
    }
} catch(e) {
    print(`Error: ${e}`);
}

print("\nExample completed!");
