// Simple test script to verify that the Rhai engine is working

print("Hello, world!");

// Try to access the PostgreSQL installer functions
print("\nTrying to access PostgreSQL installer functions...");

// Check if the pg_install function is defined
print("pg_install function is defined: " + is_def_fn("pg_install"));

// Print the available functions
print("\nAvailable functions:");
print("pg_connect: " + is_def_fn("pg_connect"));
print("pg_ping: " + is_def_fn("pg_ping"));
print("pg_reset: " + is_def_fn("pg_reset"));
print("pg_execute: " + is_def_fn("pg_execute"));
print("pg_query: " + is_def_fn("pg_query"));
print("pg_query_one: " + is_def_fn("pg_query_one"));
print("pg_install: " + is_def_fn("pg_install"));
print("pg_create_database: " + is_def_fn("pg_create_database"));
print("pg_execute_sql: " + is_def_fn("pg_execute_sql"));
print("pg_is_running: " + is_def_fn("pg_is_running"));

print("\nTest completed successfully!");
