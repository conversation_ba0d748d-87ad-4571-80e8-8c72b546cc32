// 03_text_replacer.rhai
// Tests for text replacement functions in the Text module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

print("=== Testing Text Replacement Functions ===");

// Test TextReplacer with simple replacements
print("Testing TextReplacer with simple replacements...");

// Test case 1: Basic replacement
let replacer1 = text_replacer_new()
    .pattern("foo")
    .replacement("bar")
    .build();

let input1 = "foo bar foo";
let expected_output1 = "bar bar bar";
let output1 = replacer1.replace(input1);
assert_eq(output1, expected_output1, "Basic replacement should work correctly");
print("✓ TextReplacer: Basic replacement works correctly");

// Test case 2: Multiple replacements
let replacer2 = text_replacer_new()
    .pattern("foo")
    .replacement("bar")
    .and()
    .pattern("baz")
    .replacement("qux")
    .build();

let input2 = "foo baz foo";
let expected_output2 = "bar qux bar";
let output2 = replacer2.replace(input2);
assert_eq(output2, expected_output2, "Multiple replacements should work correctly");
print("✓ TextReplacer: Multiple replacements work correctly");

// Test TextReplacer with regex replacements
print("\nTesting TextReplacer with regex replacements...");

// Test case 3: Basic regex replacement
let replacer3 = text_replacer_new()
    .pattern("f.o")
    .replacement("bar")
    .regex(true)
    .build();

let input3 = "foo fao fio";
let output3 = replacer3.replace(input3);
// The regex "f.o" matches "foo", "fao", and "fio"
let expected_output3 = "bar bar bar";
assert_eq(output3, expected_output3, "Basic regex replacement should work correctly");
print("✓ TextReplacer: Basic regex replacement works correctly");

// Test case 4: Case-insensitive regex replacement
let replacer4 = text_replacer_new()
    .pattern("foo")
    .replacement("bar")
    .regex(true)
    .case_insensitive(true)
    .build();

let input4 = "FOO foo Foo";
let expected_output4 = "bar bar bar";
let output4 = replacer4.replace(input4);
assert_eq(output4, expected_output4, "Case-insensitive regex replacement should work correctly");
print("✓ TextReplacer: Case-insensitive regex replacement works correctly");

// Test TextReplacer with file operations
print("\nTesting TextReplacer with file operations...");

// Create a temporary file for testing
let test_dir = "rhai_test_text_replacer";
mkdir(test_dir);
let test_file = `${test_dir}/test_file.txt`;
let test_output_file = `${test_dir}/test_output_file.txt`;

// Write test content to the file
let test_content = "This is a test file with foo and bar.";
file_write(test_file, test_content);

// Test case 5: Replace in file and get result as string
let replacer5 = text_replacer_new()
    .pattern("foo")
    .replacement("baz")
    .build();

let expected_output5 = "This is a test file with baz and bar.";
let output5 = replacer5.replace_file(test_file);
assert_eq(output5, expected_output5, "replace_file should return the replaced content");
print("✓ TextReplacer: replace_file works correctly");

// Test case 6: Replace in file and write to a new file
replacer5.replace_file_to(test_file, test_output_file);
let output_content = file_read(test_output_file);
assert_eq(output_content, expected_output5, "replace_file_to should write the replaced content to a new file");
print("✓ TextReplacer: replace_file_to works correctly");

// Test case 7: Replace in file and write back to the same file
// First, update the test file with the replaced content
file_write(test_file, expected_output5);

let replacer6 = text_replacer_new()
    .pattern("baz")
    .replacement("qux")
    .build();

replacer6.replace_file_in_place(test_file);
let updated_content = file_read(test_file);
let expected_output6 = "This is a test file with qux and bar.";
assert_eq(updated_content, expected_output6, "replace_file_in_place should update the file in place");
print("✓ TextReplacer: replace_file_in_place works correctly");

// Clean up
delete(test_dir);
print("✓ Cleanup: Test directory removed");

print("\nAll text replacement tests completed successfully!");
