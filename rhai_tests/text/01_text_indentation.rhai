// 01_text_indentation.rhai
// Tests for text indentation functions in the Text module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

print("=== Testing Text Indentation Functions ===");

// Test dedent function
print("Testing dedent()...");

// Test case 1: Basic indentation
let indented_text1 = "    line 1\n    line 2\n        line 3";
let expected_dedented1 = "line 1\nline 2\n    line 3";
let dedented1 = dedent(indented_text1);
assert_eq(dedented1, expected_dedented1, "Basic indentation should be removed correctly");
print("✓ dedent(): Basic indentation removed correctly");

// Test case 2: Mixed indentation
let indented_text2 = "  line 1\n    line 2\n  line 3";
let expected_dedented2 = "line 1\n  line 2\nline 3";
let dedented2 = dedent(indented_text2);
assert_eq(dedented2, expected_dedented2, "Mixed indentation should be handled correctly");
print("✓ dedent(): Mixed indentation handled correctly");

// Test case 3: Empty lines
let indented_text3 = "    line 1\n\n    line 3";
let expected_dedented3 = "line 1\n\nline 3";
let dedented3 = dedent(indented_text3);
assert_eq(dedented3, expected_dedented3, "Empty lines should be preserved");
print("✓ dedent(): Empty lines preserved correctly");

// Test case 4: No indentation
let text4 = "line 1\nline 2\nline 3";
let dedented4 = dedent(text4);
assert_eq(dedented4, text4, "Text without indentation should remain unchanged");
print("✓ dedent(): Text without indentation remains unchanged");

// Test case 5: Single line
let indented_text5 = "    single line";
let expected_dedented5 = "single line";
let dedented5 = dedent(indented_text5);
assert_eq(dedented5, expected_dedented5, "Single line indentation should be removed");
print("✓ dedent(): Single line indentation removed correctly");

// Test prefix function
print("\nTesting prefix()...");

// Test case 1: Basic prefix
let text1 = "line 1\nline 2\nline 3";
let expected_prefixed1 = "    line 1\n    line 2\n    line 3";
let prefixed1 = prefix(text1, "    ");
assert_eq(prefixed1, expected_prefixed1, "Basic prefix should be added correctly");
print("✓ prefix(): Basic prefix added correctly");

// Test case 2: Empty prefix
let text2 = "line 1\nline 2\nline 3";
let prefixed2 = prefix(text2, "");
assert_eq(prefixed2, text2, "Empty prefix should not change the text");
print("✓ prefix(): Empty prefix doesn't change the text");

// Test case 3: Prefix with empty lines
let text3 = "line 1\n\nline 3";
let expected_prefixed3 = "    line 1\n    \n    line 3";
let prefixed3 = prefix(text3, "    ");
assert_eq(prefixed3, expected_prefixed3, "Prefix should be added to empty lines");
print("✓ prefix(): Prefix added to empty lines correctly");

// Test case 4: Single line
let text4 = "single line";
let expected_prefixed4 = "    single line";
let prefixed4 = prefix(text4, "    ");
assert_eq(prefixed4, expected_prefixed4, "Prefix should be added to single line");
print("✓ prefix(): Prefix added to single line correctly");

// Test case 5: Non-space prefix
let text5 = "line 1\nline 2\nline 3";
let expected_prefixed5 = ">>> line 1\n>>> line 2\n>>> line 3";
let prefixed5 = prefix(text5, ">>> ");
assert_eq(prefixed5, expected_prefixed5, "Non-space prefix should be added correctly");
print("✓ prefix(): Non-space prefix added correctly");

// Test combining dedent and prefix
print("\nTesting combination of dedent() and prefix()...");

let indented_text = "    line 1\n    line 2\n        line 3";
let dedented = dedent(indented_text);
let prefixed = prefix(dedented, "  ");
let expected_result = "  line 1\n  line 2\n      line 3";
assert_eq(prefixed, expected_result, "Combination of dedent and prefix should work correctly");
print("✓ dedent() + prefix(): Combination works correctly");

print("\nAll text indentation tests completed successfully!");
