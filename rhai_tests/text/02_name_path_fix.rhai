// 02_name_path_fix.rhai
// Tests for filename and path normalization functions in the Text module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

print("=== Testing Filename and Path Normalization Functions ===");

// Test name_fix function
print("Testing name_fix()...");

// Test case 1: Basic name fixing
let name1 = "Hello World";
let expected_fixed1 = "hello_world";
let fixed1 = name_fix(name1);
assert_eq(fixed1, expected_fixed1, "Spaces should be replaced with underscores and converted to lowercase");
print("✓ name_fix(): Basic name fixing works correctly");

// Test case 2: Special characters
let name2 = "File-Name.txt";
let expected_fixed2 = "file_name.txt";
let fixed2 = name_fix(name2);
assert_eq(fixed2, expected_fixed2, "Hyphens should be replaced with underscores");
print("✓ name_fix(): Special characters handled correctly");

// Test case 3: Multiple special characters
let name3 = "Test!@#$%^&*()";
let expected_fixed3 = "test_";
let fixed3 = name_fix(name3);
assert_eq(fixed3, expected_fixed3, "Multiple special characters should be collapsed into a single underscore");
print("✓ name_fix(): Multiple special characters handled correctly");

// Test case 4: Non-ASCII characters
let name4 = "Café";
let expected_fixed4 = "caf";
let fixed4 = name_fix(name4);
assert_eq(fixed4, expected_fixed4, "Non-ASCII characters should be removed");
print("✓ name_fix(): Non-ASCII characters removed correctly");

// Test case 5: Uppercase conversion
let name5 = "UPPERCASE";
let expected_fixed5 = "uppercase";
let fixed5 = name_fix(name5);
assert_eq(fixed5, expected_fixed5, "Uppercase should be converted to lowercase");
print("✓ name_fix(): Uppercase conversion works correctly");

// Test path_fix function
print("\nTesting path_fix()...");

// Test case 1: Path ending with /
let path1 = "/path/to/directory/";
let expected_fixed_path1 = "/path/to/directory/";
let fixed_path1 = path_fix(path1);
assert_eq(fixed_path1, expected_fixed_path1, "Path ending with / should remain unchanged");
print("✓ path_fix(): Path ending with / remains unchanged");

// Test case 2: Single filename
let path2 = "filename.txt";
let expected_fixed_path2 = "filename.txt";
let fixed_path2 = path_fix(path2);
assert_eq(fixed_path2, expected_fixed_path2, "Single filename should be fixed");
print("✓ path_fix(): Single filename fixed correctly");

// Test case 3: Path with filename
let path3 = "/path/to/File Name.txt";
let expected_fixed_path3 = "/path/to/file_name.txt";
let fixed_path3 = path_fix(path3);
assert_eq(fixed_path3, expected_fixed_path3, "Only the filename part of the path should be fixed");
print("✓ path_fix(): Path with filename fixed correctly");

// Test case 4: Relative path
let path4 = "./relative/path/to/DOCUMENT-123.pdf";
let expected_fixed_path4 = "./relative/path/to/document_123.pdf";
let fixed_path4 = path_fix(path4);
assert_eq(fixed_path4, expected_fixed_path4, "Relative path should be handled correctly");
print("✓ path_fix(): Relative path handled correctly");

// Test case 5: Path with special characters in filename
let path5 = "/path/with/[special]<chars>.txt";
let expected_fixed_path5 = "/path/with/_special_chars_.txt";
let fixed_path5 = path_fix(path5);
assert_eq(fixed_path5, expected_fixed_path5, "Special characters in filename should be handled correctly");
print("✓ path_fix(): Path with special characters in filename handled correctly");

print("\nAll filename and path normalization tests completed successfully!");
