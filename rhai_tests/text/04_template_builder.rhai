// 04_template_builder.rhai
// Tests for template rendering functions in the Text module

// Custom assert function
fn assert_true(condition, message) {
    if !condition {
        print(`ASSERTION FAILED: ${message}`);
        throw message;
    }
}

// Custom assert_eq function
fn assert_eq(actual, expected, message) {
    if actual != expected {
        print(`ASSERTION FAILED: ${message}`);
        print(`Expected: "${expected}"`);
        print(`Actual: "${actual}"`);
        throw message;
    }
}

print("=== Testing Template Rendering Functions ===");

// Create a temporary directory for testing
let test_dir = "rhai_test_template";
mkdir(test_dir);

// Test TemplateBuilder with string template
print("Testing TemplateBuilder with string template...");

// Test case 1: Basic template with string variable
let template1 = "Hello, {{ name }}!";
let builder1 = template_builder_open(template1);
builder1.add_var("name", "World");
let expected_output1 = "Hello, World!";
let output1 = builder1.render();
assert_eq(output1, expected_output1, "Basic template with string variable should render correctly");
print("✓ TemplateBuilder: Basic template with string variable renders correctly");

// Test case 2: Template with multiple variables of different types
let template2 = "{{ name }} is {{ age }} years old and {{ is_active ? 'active' : 'inactive' }}.";
let builder2 = template_builder_open(template2);
builder2.add_var("name", "John");
builder2.add_var("age", 30);
builder2.add_var("is_active", true);
let expected_output2 = "John is 30 years old and active.";
let output2 = builder2.render();
assert_eq(output2, expected_output2, "Template with multiple variables should render correctly");
print("✓ TemplateBuilder: Template with multiple variables renders correctly");

// Test case 3: Template with array variable
let template3 = "Items: {% for item in items %}{{ item }}{% if !loop.last %}, {% endif %}{% endfor %}";
let builder3 = template_builder_open(template3);
let items = ["apple", "banana", "cherry"];
builder3.add_var("items", items);
let expected_output3 = "Items: apple, banana, cherry";
let output3 = builder3.render();
assert_eq(output3, expected_output3, "Template with array variable should render correctly");
print("✓ TemplateBuilder: Template with array variable renders correctly");

// Test case 4: Template with map variable
let template4 = "User: {{ user.name }}, Age: {{ user.age }}";
let builder4 = template_builder_open(template4);
let user = #{
    name: "Alice",
    age: 25
};
builder4.add_vars(user);
let expected_output4 = "User: Alice, Age: 25";
let output4 = builder4.render();
assert_eq(output4, expected_output4, "Template with map variable should render correctly");
print("✓ TemplateBuilder: Template with map variable renders correctly");

// Test TemplateBuilder with file operations
print("\nTesting TemplateBuilder with file operations...");

// Create a template file
let template_file = `${test_dir}/template.txt`;
let template_content = "Hello, {{ name }}! You are {{ age }} years old.";
file_write(template_file, template_content);

// Test case 5: Template from file
let builder5 = template_builder_open(template_file);
builder5.add_var("name", "Bob");
builder5.add_var("age", 40);
let expected_output5 = "Hello, Bob! You are 40 years old.";
let output5 = builder5.render();
assert_eq(output5, expected_output5, "Template from file should render correctly");
print("✓ TemplateBuilder: Template from file renders correctly");

// Test case 6: Render to file
let output_file = `${test_dir}/output.txt`;
builder5.render_to_file(output_file);
let output_content = file_read(output_file);
assert_eq(output_content, expected_output5, "render_to_file should write the rendered content to a file");
print("✓ TemplateBuilder: render_to_file works correctly");

// Clean up
delete(test_dir);
print("✓ Cleanup: Test directory removed");

print("\nAll template rendering tests completed successfully!");
