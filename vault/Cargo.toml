[package]
name = "vault"
version = "0.1.0"
edition = "2024"

[features]
native = ["kv/native"]
wasm = ["kv/web"]

[dependencies]
getrandom = { version = "0.3.3", features = ["wasm_js"] }
rand = "0.9.1"
# We need to pull v0.2.x to enable the "js" feature for wasm32 builds
getrandom_old = { package = "getrandom", version = "0.2.16", features = ["js"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
chacha20poly1305 = "0.10.1"
k256 = { version = "0.13.4", features = ["ecdh"] }
sha2 = "0.10.9"
kv = { git = "https://git.threefold.info/samehabouelsaad/sal-modular", package = "kvstore", rev = "9dce815daa" }
bincode = { version = "2.0.1", features = ["serde"] }
pbkdf2 = "0.12.2"
