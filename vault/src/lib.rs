pub mod error;
pub mod key;
pub mod keyspace;

#[cfg(not(target_arch = "wasm32"))]
use std::path::{Path, PathBuf};

use crate::{error::Error, key::symmetric::SymmetricKey, keyspace::KeySpace};

/// Vault is a 2 tiered key-value store. That is, it is a collection of [`spaces`](KeySpace), where
/// each [`space`](KeySpace) is itself an encrypted key-value store
pub struct Vault {
    #[cfg(not(target_arch = "wasm32"))]
    path: PathBuf,
}

#[cfg(not(target_arch = "wasm32"))]
impl Vault {
    /// Create a new store at the given path, creating the path if it does not exist yet.
    pub async fn new(path: &Path) -> Result<Self, Error> {
        if path.exists() {
            if !path.is_dir() {
                return Err(Error::IOError(std::io::Error::new(
                    std::io::ErrorKind::InvalidInput,
                    "expected directory",
                )));
            }
        } else {
            std::fs::create_dir_all(path)?;
        }
        Ok(Self {
            path: path.to_path_buf(),
        })
    }
}

impl Vault {
    /// Open a keyspace with the given name
    pub async fn open_keyspace(&self, name: &str, password: &str) -> Result<KeySpace, Error> {
        let encryption_key = SymmetricKey::derive_from_password(password);
        #[cfg(not(target_arch = "wasm32"))]
        {
            let path = self.path.join(name);
            KeySpace::open(&path, encryption_key).await
        }
        #[cfg(target_arch = "wasm32")]
        {
            KeySpace::open(name, encryption_key).await
        }
    }
}
