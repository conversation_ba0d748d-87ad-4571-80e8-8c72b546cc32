use crate::{error::Error, key::Key};

/// KeySpace represents an IndexDB keyspace
pub struct KeySpace {}

impl KeySpace {
    /// Get a [`Key`] previously stored under the provided name.
    async fn get(&self, key: &str) -> Result<Option<Key>, Error> {
        todo!();
    }

    /// Store a [`Key`] under the provided name.
    async fn set(&self, key: &str, value: Key) -> Result<(), Error> {
        todo!();
    }

    /// Delete the [`Key`] stored under the provided name.
    async fn delete(&self, key: &str) -> Result<(), Error> {
        todo!();
    }

    /// Iterate over all stored [`keys`](Key) in the KeySpace
    async fn iter(&self) -> Result<impl Iterator<Item = (String, Key)>, Error> {
        todo!()
    }
}
