#[derive(Debug)]
/// Errors encountered while using the vault
pub enum Error {
    /// An error during cryptographic operations
    Crypto(CryptoError),
    /// An error while performing an I/O operation
    IOError(std::io::Error),
    /// A corrupt keyspace is returned if a keyspace can't be decrypted
    CorruptKeyspace,
    /// An error in the used key value store
    KV(kv::error::KVError),
    /// An error while encoding/decoding the keyspace.
    Coding,
}

impl core::fmt::Display for Error {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        match self {
            Error::Crypto(e) => f.write_fmt(format_args!("crypto: {e}")),
            Error::IOError(e) => f.write_fmt(format_args!("io: {e}")),
            Error::CorruptKeyspace => f.write_str("corrupt keyspace"),
            Error::KV(e) => f.write_fmt(format_args!("kv: {e}")),
            Error::Coding => f.write_str("keyspace coding failed"),
        }
    }
}

impl core::error::Error for Error {}

#[derive(Debug)]
/// Errors generated by the vault or keys.
///
/// These errors are intentionally vague to avoid issues such as padding oracles.
pub enum CryptoError {
    /// Key size is not valid for this type of key
    InvalidKeySize,
    /// Something went wrong while trying to encrypt data
    EncryptionFailed,
    /// Something went wrong while trying to decrypt data
    DecryptionFailed,
    /// Something went wrong while trying to sign a message
    SigningError,
    /// The signature is invalid for this message and public key
    SignatureFailed,
    /// The signature does not have the expected size
    InvalidSignatureSize,
    /// Trying to load a key which is not the expected format,
    InvalidKey,
}

impl core::fmt::Display for CryptoError {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        match self {
            CryptoError::InvalidKeySize => f.write_str("provided key is not the correct size"),
            CryptoError::EncryptionFailed => f.write_str("encryption failure"),
            CryptoError::DecryptionFailed => f.write_str("decryption failure"),
            CryptoError::SigningError => f.write_str("signature generation failure"),
            CryptoError::SignatureFailed => f.write_str("signature verification failure"),
            CryptoError::InvalidSignatureSize => {
                f.write_str("provided signature does not have the expected size")
            }
            CryptoError::InvalidKey => f.write_str("the provided bytes are not a valid key"),
        }
    }
}

impl core::error::Error for CryptoError {}

impl From<CryptoError> for Error {
    fn from(value: CryptoError) -> Self {
        Self::Crypto(value)
    }
}

impl From<std::io::Error> for Error {
    fn from(value: std::io::Error) -> Self {
        Self::IOError(value)
    }
}

impl From<kv::error::KVError> for Error {
    fn from(value: kv::error::KVError) -> Self {
        Self::KV(value)
    }
}

impl From<bincode::error::DecodeError> for Error {
    fn from(_: bincode::error::DecodeError) -> Self {
        Self::Coding
    }
}

impl From<bincode::error::EncodeError> for Error {
    fn from(_: bincode::error::EncodeError) -> Self {
        Self::Coding
    }
}

impl From<k256::ecdsa::Error> for CryptoError {
    fn from(_: k256::ecdsa::Error) -> Self {
        Self::InvalidKey
    }
}

impl From<k256::elliptic_curve::Error> for CryptoError {
    fn from(_: k256::elliptic_curve::Error) -> Self {
        Self::InvalidKey
    }
}
