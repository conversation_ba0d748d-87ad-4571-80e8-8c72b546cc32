// Redis Authentication Example
//
// This example demonstrates how to use the Redis client module with authentication:
// - Create a Redis configuration with authentication
// - Connect to Redis using the configuration
// - Perform basic operations
//
// Prerequisites:
// - Redis server must be running with authentication enabled
// - You need to know the password for the Redis server

// Helper function to check if Redis is available
fn is_redis_available() {
    try {
        // Try to execute a simple ping
        let ping_result = redis_ping();
        return ping_result == "PONG";
    } catch(err) {
        print(`Redis connection error: ${err}`);
        return false;
    }
}

// Main function
fn main() {
    print("=== Redis Authentication Example ===");

    // Check if Redis is available
    let redis_available = is_redis_available();
    if !redis_available {
        print("Redis server is not available. Please check your connection settings.");
        return;
    }

    print("✓ Redis server is available");

    // Step 1: Create a Redis configuration with authentication
    print("\n1. Creating Redis configuration with authentication...");
    
    // Replace these values with your actual Redis credentials
    let redis_host = "localhost";
    let redis_port = 6379;
    let redis_password = "your_password_here"; // Replace with your actual password
    
    // Create a configuration builder
    let config = redis_config_builder();
    
    // Configure the connection
    config = config.host(redis_host);
    config = config.port(redis_port);
    config = config.password(redis_password);
    
    // Build the connection URL
    let connection_url = config.build_connection_url();
    print(`✓ Created Redis configuration with URL: ${connection_url}`);
    
    // Step 2: Connect to Redis using the configuration
    print("\n2. Connecting to Redis with authentication...");
    
    try {
        let connect_result = redis_connect_with_config(config);
        if (connect_result) {
            print("✓ Successfully connected to Redis with authentication");
        } else {
            print("✗ Failed to connect to Redis with authentication");
            return;
        }
    } catch(err) {
        print(`✗ Error connecting to Redis: ${err}`);
        return;
    }
    
    // Step 3: Perform basic operations
    print("\n3. Performing basic operations...");
    
    // Set a key
    let set_key = "auth_example_key";
    let set_value = "This value was set using authentication";
    
    try {
        let set_result = redis_set(set_key, set_value);
        if (set_result) {
            print(`✓ Successfully set key '${set_key}'`);
        } else {
            print(`✗ Failed to set key '${set_key}'`);
        }
    } catch(err) {
        print(`✗ Error setting key: ${err}`);
    }
    
    // Get the key
    try {
        let get_result = redis_get(set_key);
        if (get_result == set_value) {
            print(`✓ Successfully retrieved key '${set_key}': '${get_result}'`);
        } else {
            print(`✗ Retrieved incorrect value for key '${set_key}': '${get_result}'`);
        }
    } catch(err) {
        print(`✗ Error getting key: ${err}`);
    }
    
    // Delete the key
    try {
        let del_result = redis_del(set_key);
        if (del_result) {
            print(`✓ Successfully deleted key '${set_key}'`);
        } else {
            print(`✗ Failed to delete key '${set_key}'`);
        }
    } catch(err) {
        print(`✗ Error deleting key: ${err}`);
    }
    
    // Verify the key is gone
    try {
        let verify_result = redis_get(set_key);
        if (verify_result == "") {
            print(`✓ Verified key '${set_key}' was deleted`);
        } else {
            print(`✗ Key '${set_key}' still exists with value: '${verify_result}'`);
        }
    } catch(err) {
        print(`✗ Error verifying deletion: ${err}`);
    }
    
    print("\nExample completed successfully!");
}

// Run the main function
main();
