// Basic example of using the Zinit client in Rhai

// Socket path for Zinit
let socket_path = "/var/run/zinit.sock";

// List all services
print("Listing all services:");
let services = zinit_list(socket_path);

if services.is_empty() {
    print("No services found.");
} else {
    // Iterate over the keys of the map
    for name in services.keys() {
        let state = services[name];
        print(`${name}: ${state}`);
    }
}

// Get status of a specific service
let service_name = "test";
print(`Getting status for ${service_name}:`);

try {
    let status = zinit_status(socket_path, service_name);
    print(`Service: ${status.name}`);
    print(`PID: ${status.pid}`);
    print(`State: ${status.state}`);
    print(`Target: ${status.target}`);
    print("Dependencies:");
    
    for (dep, state) in status.after.keys() {
        print(`  ${dep}: ${state}`);
    }
} catch(err) {
    print(`Error getting status: ${err}`);
}

// Create a new service
print("\nCreating a new service:");
let new_service = "rhai-test-service";
let exec_command = "echo 'Hello from Rhai'";
let oneshot = true;

try {
    let result = zinit_create_service(socket_path, new_service, exec_command, oneshot);
    print(`Service created: ${result}`);
    
    // Monitor the service
    print("\nMonitoring the service:");
    let monitor_result = zinit_monitor(socket_path, new_service);
    print(`Service monitored: ${monitor_result}`);
    
    // Start the service
    print("\nStarting the service:");
    let start_result = zinit_start(socket_path, new_service);
    print(`Service started: ${start_result}`);
    
    // Get logs for a specific service
    print("\nGetting logs:");
    let logs = zinit_logs(socket_path, new_service);
    
    for log in logs {
        print(log);
    }
    // Clean up
    print("\nCleaning up:");
    let stop_result = zinit_stop(socket_path, new_service);
    print(`Service stopped: ${stop_result}`);
    
    let forget_result = zinit_forget(socket_path, new_service);
    print(`Service forgotten: ${forget_result}`);
    
    let delete_result = zinit_delete_service(socket_path, new_service);
    print(`Service deleted: ${delete_result}`);
} catch(err) {
    print(`Error: ${err}`);
}
