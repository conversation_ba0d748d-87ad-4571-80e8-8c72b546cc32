// Basic example of using the Mycelium client in Rhai

// API URL for Mycelium
let api_url = "http://localhost:8989";

// Get node information
print("Getting node information:");
try {
    let node_info = mycelium_get_node_info(api_url);
    print(`Node subnet: ${node_info.nodeSubnet}`);
    print(`Node public key: ${node_info.nodePubkey}`);
} catch(err) {
    print(`Error getting node info: ${err}`);
}

// List all peers
print("\nListing all peers:");
try {
    let peers = mycelium_list_peers(api_url);
    
    if peers.is_empty() {
        print("No peers connected.");
    } else {
        for peer in peers {
            print(`Peer Endpoint: ${peer.endpoint.proto}://${peer.endpoint.socketAddr}`);
            print(`  Type: ${peer.type}`);
            print(`  Connection State: ${peer.connectionState}`);
            print(`  Bytes sent: ${peer.txBytes}`);
            print(`  Bytes received: ${peer.rxBytes}`);
        }
    }
} catch(err) {
    print(`Error listing peers: ${err}`);
}

// Add a new peer
print("\nAdding a new peer:");
let new_peer_address = "tcp://************:9651";
try {
    let result = mycelium_add_peer(api_url, new_peer_address);
    print(`Peer added: ${result.success}`);
} catch(err) {
    print(`Error adding peer: ${err}`);
}

// List selected routes
print("\nListing selected routes:");
try {
    let routes = mycelium_list_selected_routes(api_url);
    
    if routes.is_empty() {
        print("No selected routes.");
    } else {
        for route in routes {
            print(`Subnet: ${route.subnet}`);
            print(`  Next hop: ${route.nextHop}`);
            print(`  Metric: ${route.metric}`);
        }
    }
} catch(err) {
    print(`Error listing routes: ${err}`);
}

// List fallback routes
print("\nListing fallback routes:");
try {
    let routes = mycelium_list_fallback_routes(api_url);
    
    if routes.is_empty() {
        print("No fallback routes.");
    } else {
        for route in routes {
            print(`Subnet: ${route.subnet}`);
            print(`  Next hop: ${route.nextHop}`);
            print(`  Metric: ${route.metric}`);
        }
    }
} catch(err) {
    print(`Error listing fallback routes: ${err}`);
}

// Send a message
// TO SEND A MESSAGE FILL IN THE DESTINATION IP ADDRESS
// -----------------------------------------------------//
// print("\nSending a message:");
// let destination = < FILL IN CORRECT DEST IP >
// let topic = "test";
// let message = "Hello from Rhai!";
// let deadline_secs = 60;

// try {
//     let result = mycelium_send_message(api_url, destination, topic, message, deadline_secs);
//     print(`Message sent: ${result.success}`);
//     if result.id {
//         print(`Message ID: ${result.id}`);
//     }
// } catch(err) {
//     print(`Error sending message: ${err}`);
// }

// Receive messages
// RECEIVING MESSAGES SHOULD BE DONE ON THE DESTINATION NODE FROM THE CALL ABOVE
// -----------------------------------------------------------------------------//
// print("\nReceiving messages:");
// let receive_topic = "test";
// let count = 5;

// try {
//     let messages = mycelium_receive_messages(api_url, receive_topic, count);
    
//     if messages.is_empty() {
//         print("No messages received.");
//     } else {
//         for msg in messages {
//             print(`Message from: ${msg.source}`);
//             print(`  Topic: ${msg.topic}`);
//             print(`  Content: ${msg.content}`);
//             print(`  Timestamp: ${msg.timestamp}`);
//         }
//     }
// } catch(err) {
//     print(`Error receiving messages: ${err}`);
// }

// Remove a peer
print("\nRemoving a peer:");
let peer_id = "tcp://************:9651"; // This is the peer we added earlier
try {
    let result = mycelium_remove_peer(api_url, peer_id);
    print(`Peer removed: ${result.success}`);
} catch(err) {
    print(`Error removing peer: ${err}`);
}