// Script to receive Mycelium messages

// API URL for Mycelium
let api_url = "http://localhost:2222";

// Receive messages
// This script will listen for messages on a specific topic.
// Ensure the sender script is using the same topic.
// -----------------------------------------------------------------------------//
print("\nReceiving messages:");
let receive_topic = "test_topic";
let wait_deadline_secs = 100; 

print(`Listening for messages on topic '${receive_topic}'...`);
try {
    let messages = mycelium_receive_messages(api_url, receive_topic, wait_deadline_secs);
    
    if messages.is_empty() {
        // print("No new messages received in this poll.");
    } else {
        print("Received a message:");
        print(`  Message id: ${messages.id}`);
        print(`  Message from: ${messages.srcIp}`);
        print(`  Topic: ${messages.topic}`);
        print(`  Payload: ${messages.payload}`);
    }
} catch(err) {
    print(`Error receiving messages: ${err}`);
}

print("Finished attempting to receive messages.");