// <PERSON>ript to send a Mycelium message

// API URL for Mycelium
let api_url = "http://localhost:1111";

// Send a message
// TO SEND A MESSAGE FILL IN THE DESTINATION IP ADDRESS
// -----------------------------------------------------//
print("\nSending a message:");
let destination = "50e:6d75:4568:366e:f75:2ac3:bbb1:3fdd"; // IMPORTANT: Replace with the actual destination IP address
let topic = "test_topic";
let message = "Hello from Rhai sender!";
let deadline_secs = -10; // Seconds we wait for a reply

try {
    print(`Attempting to send message to ${destination} on topic '${topic}'`);
    let result = mycelium_send_message(api_url, destination, topic, message, deadline_secs);
    print(`result: ${result}`);
    print(`Message sent: ${result.success}`);
    if result.id != "" {
        print(`Message ID: ${result.id}`);
    }
} catch(err) {
    print(`Error sending message: ${err}`);
}