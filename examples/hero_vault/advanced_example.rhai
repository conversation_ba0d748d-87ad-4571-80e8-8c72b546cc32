// Advanced Rhai script example for Hero Vault Cryptography Module
// This script demonstrates conditional logic, error handling, and more complex operations

// Function to create a key space with error handling
fn setup_key_space(name, password) {
    print("Attempting: Create key space: " + name);
    let result = create_key_space(name, password);
    
    if result {
        print("✅ Create key space succeeded!");
        return true;
    } else {
        print("❌ Create key space failed!");
    }
    
    return false;
}

// Function to create and select a keypair
fn setup_keypair(name, password) {
    print("Attempting: Create keypair: " + name);
    let result = create_keypair(name, password);
    
    if result {
        print("✅ Create keypair succeeded!");
        
        print("Attempting: Select keypair: " + name);
        let selected = select_keypair(name);
        
        if selected {
            print("✅ Select keypair succeeded!");
            return true;
        } else {
            print("❌ Select keypair failed!");
        }
    } else {
        print("❌ Create keypair failed!");
    }
    
    return false;
}

// Function to sign multiple messages
fn sign_messages(messages) {
    let signatures = [];
    
    for message in messages {
        print("Signing message: " + message);
        print("Attempting: Sign message");
        let signature = sign(message);
        
        if signature != "" {
            print("✅ Sign message succeeded!");
            signatures.push(#{
                message: message,
                signature: signature
            });
        } else {
            print("❌ Sign message failed!");
        }
    }
    
    return signatures;
}

// Function to verify signatures
fn verify_signatures(signed_messages) {
    let results = [];
    
    for item in signed_messages {
        let message = item.message;
        let signature = item.signature;
        
        print("Verifying signature for: " + message);
        print("Attempting: Verify signature");
        let is_valid = verify(message, signature);
        
        if is_valid {
            print("✅ Verify signature succeeded!");
        } else {
            print("❌ Verify signature failed!");
        }
        
        results.push(#{
            message: message,
            valid: is_valid
        });
    }
    
    return results;
}

// Function to encrypt multiple messages
fn encrypt_messages(messages) {
    // Generate a symmetric key
    print("Attempting: Generate symmetric key");
    let key = generate_key();
    
    if key == "" {
        print("❌ Generate symmetric key failed!");
        return [];
    }
    
    print("✅ Generate symmetric key succeeded!");
    print("Using key: " + key);
    let encrypted_messages = [];
    
    for message in messages {
        print("Encrypting message: " + message);
        print("Attempting: Encrypt message");
        let encrypted = encrypt(key, message);
        
        if encrypted != "" {
            print("✅ Encrypt message succeeded!");
            encrypted_messages.push(#{
                original: message,
                encrypted: encrypted,
                key: key
            });
        } else {
            print("❌ Encrypt message failed!");
        }
    }
    
    return encrypted_messages;
}

// Function to decrypt messages
fn decrypt_messages(encrypted_messages) {
    let decrypted_messages = [];
    
    for item in encrypted_messages {
        let encrypted = item.encrypted;
        let key = item.key;
        let original = item.original;
        
        print("Decrypting message...");
        print("Attempting: Decrypt message");
        let decrypted = decrypt(key, encrypted);
        
        if decrypted != false {
            let success = decrypted == original;
            
            decrypted_messages.push(#{
                decrypted: decrypted,
                original: original,
                success: success
            });
            
            if success {
                print("Decryption matched original ✅");
            } else {
                print("Decryption did not match original ❌");
            }
        }
    }
    
    return decrypted_messages;
}

// Main script execution
print("=== Advanced Cryptography Script ===");

// Set up key space
let space_name = "advanced_space";
let password = "secure_password123";

if setup_key_space(space_name, password) {
    print("\n--- Key space setup complete ---\n");
    
    // Set up keypair
    if setup_keypair("advanced_keypair", password) {
        print("\n--- Keypair setup complete ---\n");
        
        // Define messages to sign
        let messages = [
            "This is the first message to sign",
            "Here's another message that needs signing",
            "And a third message for good measure"
        ];
        
        // Sign messages
        print("\n--- Signing Messages ---\n");
        let signed_messages = sign_messages(messages);
        
        // Verify signatures
        print("\n--- Verifying Signatures ---\n");
        let verification_results = verify_signatures(signed_messages);
        
        // Count successful verifications
        let successful_verifications = verification_results.filter(|r| r.valid).len();
        print("Successfully verified " + successful_verifications + " out of " + verification_results.len() + " signatures");
        
        // Encrypt messages
        print("\n--- Encrypting Messages ---\n");
        let encrypted_messages = encrypt_messages(messages);
        
        // Decrypt messages
        print("\n--- Decrypting Messages ---\n");
        let decryption_results = decrypt_messages(encrypted_messages);
        
        // Count successful decryptions
        let successful_decryptions = decryption_results.filter(|r| r.success).len();
        print("Successfully decrypted " + successful_decryptions + " out of " + decryption_results.len() + " messages");
        
        // Create Ethereum wallet
        print("\n--- Creating Ethereum Wallet ---\n");
        print("Attempting: Create Ethereum wallet");
        let wallet_created = create_ethereum_wallet();
        
        if wallet_created {
            print("✅ Create Ethereum wallet succeeded!");
            
            print("Attempting: Get Ethereum address");
            let address = get_ethereum_address();
            
            if address != "" {
                print("✅ Get Ethereum address succeeded!");
                print("Ethereum wallet address: " + address);
            } else {
                print("❌ Get Ethereum address failed!");
            }
        } else {
            print("❌ Create Ethereum wallet failed!");
        }
        
        print("\n=== Script execution completed successfully! ===");
    } else {
        print("Failed to set up keypair. Aborting script.");
    }
} else {
    print("Failed to set up key space. Aborting script.");
}