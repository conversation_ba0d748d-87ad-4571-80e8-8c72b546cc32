// Example <PERSON><PERSON> script demonstrating key space persistence for Hero Vault
// This script shows how to create, save, and load key spaces

// Step 1: Create a key space
let space_name = "persistent_space";
let password = "secure_password123";

print("Creating key space: " + space_name);
if create_key_space(space_name, password) {
    print("✓ Key space created successfully");
    
    // Step 2: Create keypairs in this space
    print("\nCreating keypairs...");
    if create_keypair("persistent_key1", password) {
        print("✓ Created first keypair");
    }
    
    if create_keypair("persistent_key2", password) {
        print("✓ Created second keypair");
    }
    
    // List all keypairs
    let keypairs = list_keypairs();
    print("Available keypairs: " + keypairs);
    
    // Step 3: Clear the session (simulate closing and reopening the CLI)
    print("\nClearing session (simulating restart)...");
    // Note: In a real script, you would exit here and run a new script
    // For demonstration purposes, we'll continue in the same script
    
    // Step 4: Load the key space from disk
    print("\nLoading key space from disk...");
    if load_key_space(space_name, password) {
        print("✓ Key space loaded successfully");
        
        // Verify the keypairs are still available
        let loaded_keypairs = list_keypairs();
        print("Keypairs after loading: " + loaded_keypairs);
        
        // Step 5: Use a keypair from the loaded space
        print("\nSelecting and using a keypair...");
        if select_keypair("persistent_key1") {
            print("✓ Selected keypair");
            
            let message = "This message was signed using a keypair from a loaded key space";
            let signature = sign(message);
            print("Message: " + message);
            print("Signature: " + signature);
            
            // Verify the signature
            let is_valid = verify(message, signature);
            if is_valid {
                print("Signature verification: ✓ Valid");
            } else {
                print("Signature verification: ✗ Invalid");
            }
        }
    } else {
        print("✗ Failed to load key space");
    }
} else {
    print("✗ Failed to create key space");
}

print("\nScript execution completed!");