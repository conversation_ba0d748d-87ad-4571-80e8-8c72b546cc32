// Example <PERSON><PERSON> script for testing contract functions with arguments on Agung network
// This script demonstrates how to use call_contract_read and call_contract_write with arguments

// Step 1: Set up wallet and network
let space_name = "agung_contract_args_demo";
let password = "secure_password123";
let private_key = "51c194d20bcd25360a3aa94426b3b60f738007e42f22e1bc97821c65c353e6d2";
let network_name = "agung";

print("=== Testing Contract Functions With Arguments on Agung Network ===\n");

// Create a key space
print("Creating key space: " + space_name);
if create_key_space(space_name, password) {
    print("✓ Key space created successfully");

    // Create a keypair
    print("\nCreating keypair...");
    if create_keypair("contract_key", password) {
        print("✓ Created contract keypair");

        // Create a wallet from the private key for the Agung network
        print("\nCreating wallet from private key for Agung network...");
        if create_wallet_from_private_key_for_network(private_key, network_name) {
            print("✓ Wallet created successfully");

            // Get the wallet address
            let wallet_address = get_wallet_address_for_network(network_name);
            print("Wallet address: " + wallet_address);

            // Check wallet balance
            print("\nChecking wallet balance...");
            let balance = get_balance(network_name, wallet_address);
            if balance != "" {
                print("Wallet balance: " + balance + " wei");

                // Define a simple ERC-20 token contract ABI (partial)
                let token_abi = `[
                    {
                        "constant": true,
                        "inputs": [],
                        "name": "name",
                        "outputs": [{"name": "", "type": "string"}],
                        "payable": false,
                        "stateMutability": "view",
                        "type": "function"
                    },
                    {
                        "constant": true,
                        "inputs": [],
                        "name": "symbol",
                        "outputs": [{"name": "", "type": "string"}],
                        "payable": false,
                        "stateMutability": "view",
                        "type": "function"
                    },
                    {
                        "constant": true,
                        "inputs": [],
                        "name": "decimals",
                        "outputs": [{"name": "", "type": "uint8"}],
                        "payable": false,
                        "stateMutability": "view",
                        "type": "function"
                    },
                    {
                        "constant": true,
                        "inputs": [{"name": "_owner", "type": "address"}],
                        "name": "balanceOf",
                        "outputs": [{"name": "balance", "type": "uint256"}],
                        "payable": false,
                        "stateMutability": "view",
                        "type": "function"
                    },
                    {
                        "constant": false,
                        "inputs": [{"name": "_to", "type": "address"}, {"name": "_value", "type": "uint256"}],
                        "name": "transfer",
                        "outputs": [{"name": "", "type": "bool"}],
                        "payable": false,
                        "stateMutability": "nonpayable",
                        "type": "function"
                    }
                ]`;

                // For this example, we'll use a test token contract on Agung
                let token_address = "0x7267B587E4416537060C6bF0B06f6Fd421106650";

                print("\nLoading contract ABI...");
                let contract = load_contract_abi(network_name, token_address, token_abi);

                if contract != "" {
                    print("✓ Contract loaded successfully");

                    // First, let's try to read some data from the contract
                    print("\nReading contract data...");

                    // Try to get token name (no arguments)
                    let token_name = call_contract_read(contract, "name");
                    print("Token name: " + token_name);

                    // Try to get token symbol (no arguments)
                    let token_symbol = call_contract_read(contract, "symbol");
                    print("Token symbol: " + token_symbol);

                    // Try to get token decimals (no arguments)
                    let token_decimals = call_contract_read(contract, "decimals");
                    print("Token decimals: " + token_decimals);

                    // Try to get token balance (with address argument)
                    print("\nCalling balanceOf with address argument...");
                    let balance = call_contract_read(contract, "balanceOf", [wallet_address]);
                    print("Token balance: " + balance);

                    // Now, let's try to execute a write function with arguments
                    print("\nExecuting contract write function with arguments...");

                    // Define a recipient address and amount for the transfer
                    // Using a random valid address on the network
                    let recipient = "******************************************";
                    let amount = 1000000; // Changed from string to number for uint256 compatibility

                    print("Attempting to transfer " + amount + " tokens to " + recipient);

                    // Call the transfer function with arguments
                    let tx_hash = call_contract_write(contract, "transfer", [recipient, amount]);

                    if tx_hash != "" {
                        print("✓ Transaction sent successfully");
                        print("Transaction hash: " + tx_hash);
                        print("You can view the transaction at: " + get_network_explorer_url(network_name) + "/tx/" + tx_hash);
                    } else {
                        print("✗ Failed to send transaction");
                        print("This could be due to insufficient funds, contract issues, or other errors.");
                    }
                } else {
                    print("✗ Failed to load contract");
                }
            } else {
                print("✗ Failed to get wallet balance");
            }
        } else {
            print("✗ Failed to create wallet from private key");
        }
    } else {
        print("✗ Failed to create keypair");
    }
} else {
    print("✗ Failed to create key space");
}

print("\nContract function with arguments test completed");
