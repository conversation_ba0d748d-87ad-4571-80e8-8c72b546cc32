// Example Rhai script for Hero Vault Cryptography Module
// This script demonstrates key management, signing, and encryption

// Step 1: Create and manage a key space
let space_name = "demo_space";
let password = "secure_password123";

print("Creating key space: " + space_name);
if create_key_space(space_name, password) {
    print("✓ Key space created successfully");
    
    // Step 2: Create and use keypairs
    print("\nCreating keypairs...");
    if create_keypair("signing_key", password) {
        print("✓ Created signing keypair");
    }
    
    if create_keypair("encryption_key", password) {
        print("✓ Created encryption keypair");
    }
    
    // List all keypairs
    let keypairs = list_keypairs();
    print("Available keypairs: " + keypairs);
    
    // Step 3: Sign a message
    print("\nPerforming signing operations...");
    if select_keypair("signing_key") {
        print("✓ Selected signing keypair");
        
        let message = "This is a secure message that needs to be signed";
        print("Message: " + message);
        
        let signature = sign(message);
        print("Signature: " + signature);
        
        // Verify the signature
        let is_valid = verify(message, signature);
        if is_valid {
            print("Signature verification: ✓ Valid");
        } else {
            print("Signature verification: ✗ Invalid");
        }
    }
    
    // Step 4: Encrypt and decrypt data
    print("\nPerforming encryption operations...");
    
    // Generate a symmetric key
    let sym_key = generate_key();
    print("Generated symmetric key: " + sym_key);
    
    // Encrypt a message
    let secret = "This is a top secret message that must be encrypted";
    print("Original message: " + secret);
    
    let encrypted_data = encrypt(sym_key, secret);
    print("Encrypted data: " + encrypted_data);
    
    // Decrypt the message
    let decrypted_data = decrypt(sym_key, encrypted_data);
    print("Decrypted message: " + decrypted_data);
    
    // Verify decryption was successful
    if decrypted_data == secret {
        print("✓ Encryption/decryption successful");
    } else {
        print("✗ Encryption/decryption failed");
    }
    
    // Step 5: Create an Ethereum wallet
    print("\nCreating Ethereum wallet...");
    if select_keypair("encryption_key") {
        print("✓ Selected keypair for Ethereum wallet");
        
        if create_ethereum_wallet() {
            print("✓ Ethereum wallet created");
            
            let address = get_ethereum_address();
            print("Ethereum address: " + address);
        }
    }
    
    print("\nScript execution completed successfully!");
}