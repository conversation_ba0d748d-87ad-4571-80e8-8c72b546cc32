// Example <PERSON><PERSON> script demonstrating loading an existing key space for Hero Vault
// This script shows how to load a previously created key space and use its keypairs

// Define the key space name and password
let space_name = "persistent_space";
let password = "secure_password123";

print("Loading existing key space: " + space_name);

// Load the key space from disk
if load_key_space(space_name, password) {
    print("✓ Key space loaded successfully");
    
    // List available keypairs
    let keypairs = list_keypairs();
    print("Available keypairs: " + keypairs);
    
    // Use both keypairs to sign different messages
    if select_keypair("persistent_key1") {
        print("\nUsing persistent_key1:");
        let message1 = "Message signed with the first keypair";
        let signature1 = sign(message1);
        print("Message: " + message1);
        print("Signature: " + signature1);
        
        let is_valid1 = verify(message1, signature1);
        if is_valid1 {
            print("Verification: ✓ Valid");
        } else {
            print("Verification: ✗ Invalid");
        }
    }
    
    if select_keypair("persistent_key2") {
        print("\nUsing persistent_key2:");
        let message2 = "Message signed with the second keypair";
        let signature2 = sign(message2);
        print("Message: " + message2);
        print("Signature: " + signature2);
        
        let is_valid2 = verify(message2, signature2);
        if is_valid2 {
            print("Verification: ✓ Valid");
        } else {
            print("Verification: ✗ Invalid");
        }
    }
    
    // Create an Ethereum wallet using one of the keypairs
    print("\nCreating Ethereum wallet from persistent keypair:");
    if select_keypair("persistent_key1") {
        if create_ethereum_wallet() {
            print("✓ Ethereum wallet created");
            
            let address = get_ethereum_address();
            print("Ethereum address: " + address);
        } else {
            print("✗ Failed to create Ethereum wallet");
        }
    }
} else {
    print("✗ Failed to load key space. Make sure you've run key_persistence_example.rhai first.");
}

print("\nScript execution completed!");