// Example Rhai script for interacting with smart contracts using Hero Vault
// This script demonstrates loading a contract ABI and interacting with a contract

// Step 1: Set up wallet and network
let space_name = "contract_demo_space";
let password = "secure_password123";

print("Creating key space: " + space_name);
if create_key_space(space_name, password) {
    print("✓ Key space created successfully");

    // Create a keypair
    print("\nCreating keypair...");
    if create_keypair("contract_key", password) {
        print("✓ Created contract keypair");
    }

    // Step 2: Create an Ethereum wallet for Gnosis Chain
    print("\nCreating Ethereum wallet...");
    if create_ethereum_wallet() {
        print("✓ Ethereum wallet created");

        let address = get_ethereum_address();
        print("Ethereum address: " + address);

        // Step 3: Define a simple ERC-20 ABI (partial)
        let erc20_abi = `[
            {
                "constant": true,
                "inputs": [],
                "name": "name",
                "outputs": [{"name": "", "type": "string"}],
                "payable": false,
                "stateMutability": "view",
                "type": "function"
            },
            {
                "constant": true,
                "inputs": [],
                "name": "symbol",
                "outputs": [{"name": "", "type": "string"}],
                "payable": false,
                "stateMutability": "view",
                "type": "function"
            },
            {
                "constant": true,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "payable": false,
                "stateMutability": "view",
                "type": "function"
            },
            {
                "constant": true,
                "inputs": [{"name": "owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "", "type": "uint256"}],
                "payable": false,
                "stateMutability": "view",
                "type": "function"
            }
        ]`;

        // Step 4: Load the contract ABI
        print("\nLoading contract ABI...");
        let contract = load_contract_abi("Gnosis", "0x4ECaBa5870353805a9F068101A40E0f32ed605C6", erc20_abi);
        if contract != "" {
            print("✓ Contract loaded successfully");

            // Step 5: Call read-only functions
            print("\nCalling read-only functions...");

            // Get token name
            let token_name = call_contract_read(contract, "name");
            print("Token name: " + token_name);

            // Get token symbol
            let token_symbol = call_contract_read(contract, "symbol");
            print("Token symbol: " + token_symbol);

            // Get token decimals
            let token_decimals = call_contract_read(contract, "decimals");
            print("Token decimals: " + token_decimals);

            // For now, we're just demonstrating the basic structure
        } else {
            print("✗ Failed to load contract");
        }
    } else {
        print("✗ Failed to create Ethereum wallet");
    }
} else {
    print("✗ Failed to create key space");
}

print("\nContract example completed");
