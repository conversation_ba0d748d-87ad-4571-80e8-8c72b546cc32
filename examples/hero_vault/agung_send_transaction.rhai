// <PERSON><PERSON><PERSON> to create an Agung wallet from a private key and send tokens
// This script demonstrates how to create a wallet from a private key and send tokens

// Define the private key and recipient address
let private_key = "0x9ecfd58eca522b0e7c109bf945966ee208cd6d593b1dc3378aedfdc60b64f512";
let recipient_address = "******************************************";

print("=== Agung Wallet Transaction Demo ===");
print(`From private key: ${private_key}`);
print(`To address: ${recipient_address}`);

// First, create a key space and keypair (required for the wallet infrastructure)
let space_name = "agung_transaction_demo";
let password = "demo_password";

// Create a new key space
if !create_key_space(space_name, password) {
    print("Failed to create key space");
    return;
}

// Create a keypair
if !create_keypair("demo_keypair", password) {
    print("Failed to create keypair");
    return;
}

// Select the keypair
if !select_keypair("demo_keypair") {
    print("Failed to select keypair");
    return;
}

print("\nCreated and selected keypair successfully");

// Clear any existing Agung wallets to avoid conflicts
if clear_wallets_for_network("agung") {
    print("Cleared existing Agung wallets");
} else {
    print("Failed to clear existing Agung wallets");
    return;
}

// Create a wallet from the private key directly
print("\n=== Creating Wallet from Private Key ===");

// Create a wallet from the private key for the Agung network
if create_wallet_from_private_key_for_network(private_key, "agung") {
    print("Successfully created wallet from private key for Agung network");
    
    // Get the wallet address
    let wallet_address = get_wallet_address_for_network("agung");
    print(`Wallet address: ${wallet_address}`);
    
    // Create a provider for the Agung network
    let provider_id = create_agung_provider();
    if provider_id != "" {
        print("Successfully created Agung provider");
        
        // Check the wallet balance first
        let wallet_address = get_wallet_address_for_network("agung");
        let balance_wei = get_balance("agung", wallet_address);
        
        if balance_wei == "" {
            print("Failed to get wallet balance");
            print("This could be due to network issues or other errors.");
            return;
        }
        
        print(`Current wallet balance: ${balance_wei} wei`);
        
        // Convert 1 AGNG to wei (1 AGNG = 10^18 wei)
        // Use string representation for large numbers
        let amount_wei_str = "1000000000000000000"; // 1 AGNG in wei as a string
        
        // Check if we have enough balance
        if parse_int(balance_wei) < parse_int(amount_wei_str) {
            print(`Insufficient balance to send ${amount_wei_str} wei (1 AGNG)`);
            print(`Current balance: ${balance_wei} wei`);
            print("Please fund the wallet before attempting to send a transaction");
            return;
        }
        
        print(`Attempting to send ${amount_wei_str} wei (1 AGNG) to ${recipient_address}`);
        
        // Send the transaction using the blocking implementation
        let tx_hash = send_eth("agung", recipient_address, amount_wei_str);
        
        if tx_hash != "" {
            print(`Transaction sent with hash: ${tx_hash}`);
            print(`You can view the transaction at: ${get_network_explorer_url("agung")}/tx/${tx_hash}`);
        } else {
            print("Transaction failed");
            print("This could be due to insufficient funds, network issues, or other errors.");
            print("Check the logs for more details.");
        }
    } else {
        print("Failed to create Agung provider");
    }
} else {
    print("Failed to create wallet from private key");
}

print("\nAgung transaction demo completed");
