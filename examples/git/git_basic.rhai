// Simplified Git Basic Operations Example

let git_tree = git_tree_new("/tmp/git"); // Using /tmp/git as base path

print("--- Git Basic Operations ---");
// print(`Base path: ${git_tree.base_path()}`); // base_path() getter would need to be exposed from Rust

let all_repos = git_tree.list();
print(`Listed ${all_repos.len()} repos.`);

// Find repos starting with "home" (adjust pattern if /tmp/git might contain other "home*" repos)
let found_repos = git_tree.find("home*"); 
print(`Found ${found_repos.len()} repos matching "home*".`);
for r in found_repos {
    print(` - Found: ${r.path()}`);
}

print("Getting/Cloning 'https://github.com/freeflowuniverse/home'...");
let repo = git_tree.get("https://github.com/freeflowuniverse/home");
print(`Repo path: ${repo.path()}`);
print(`Has changes: ${repo.has_changes()}`);

print("Performing pull & reset...");
repo.pull().reset();
print("Pull and reset complete.");
print(`Has changes after pull/reset: ${repo.has_changes()}`);

print("--- Example Finished ---");