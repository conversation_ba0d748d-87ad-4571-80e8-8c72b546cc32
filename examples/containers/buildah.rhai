// buildah.rhai
// Demonstrates using buildah to create a custom image with golang and nginx,
// then using nerdctl to run a container from that image

println("Starting buildah workflow to create a custom image...");

// Define image and container names
let base_image = "ubuntu:22.04";
let container_name = "golang-nginx-container";
let final_image_name = "custom-golang-nginx:latest";

println(`Creating container '${container_name}' from base image '${base_image}'...`);

// Create a new buildah container using the builder pattern
let builder = bah_new(container_name, base_image);

println("Enabling debug mode...");
builder.debug_mode = true;

// Update package lists and install golang and nginx
println("Installing packages (this may take a while)...");

// Update package lists
let update_result = builder.run("apt-get update -y");

// Install required packages
let install_result = builder.run("apt-get install -y golang nginx");

// Verify installations
let go_version = builder.run("go version");
println(`Go version: ${go_version.stdout}`);

let nginx_version = builder.run("nginx -v");
println(`Nginx version: ${nginx_version.stderr}`); // nginx outputs version to stderr

// Create a simple Go web application
println("Creating a simple Go web application...");

// Create a directory for the Go application
builder.run("mkdir -p /app");

// Create a simple Go web server
let go_app = `
package main

import (
    "fmt"
    "net/http"
)

func main() {
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        fmt.Fprintf(w, "Hello from Go running in a custom container!")
    })
    
    fmt.Println("Starting server on :8080")
    http.ListenAndServe(":8080", nil)
}
`;

// Write the Go application to a file using the write_content method
builder.write_content(go_app, "/app/main.go");

// Compile the Go application
builder.run("cd /app && go build -o server main.go");

// Configure nginx to proxy to the Go application
let nginx_conf = `
server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
`;

// Write the nginx configuration using the write_content method
let nginx_conf_result = builder.write_content(nginx_conf, "/etc/nginx/sites-available/default");

// Create a startup script
let startup_script = `
#!/bin/bash
# Start the Go application in the background
cd /app && ./server &
# Start nginx in the foreground
nginx -g "daemon off;"
`;

// Write the startup script using the write_content method
let startup_script_result = builder.write_content(startup_script, "/start.sh");
builder.run("chmod +x /start.sh");

// Set the entrypoint to the startup script
println("Setting entrypoint to /start.sh...");
builder.set_entrypoint("/start.sh");

// Read back the startup script to verify it was written correctly
let read_script = builder.read_content("/start.sh");
println("Startup script content verification:");
println(read_script);

// Commit the container to a new image
println(`Committing container to image '${final_image_name}'...`);
let commit_result = builder.commit(final_image_name);

// Clean up the buildah container
println("Cleaning up buildah container...");
builder.remove();

// Now use nerdctl to run a container from the new image
println("\nStarting container from the new image using nerdctl...");

// Create a container using the builder pattern
// Use localhost/ prefix to ensure nerdctl uses the local image
let local_image_name = "localhost/" + final_image_name;
println(`Using local image: ${local_image_name}`);

// Tag the image with the localhost prefix for nerdctl compatibility
println(`Tagging image as ${local_image_name}...`);
let tag_result = bah_image_tag(final_image_name, local_image_name);

// Print a command to check if the image exists in buildah
println("\nTo verify the image was created with buildah, run:");
println("buildah images");

// Note: If nerdctl cannot find the image, you may need to push it to a registry
println("\nNote: If nerdctl cannot find the image, you may need to push it to a registry:");
println("buildah push localhost/custom-golang-nginx:latest docker://localhost:5000/custom-golang-nginx:latest");
println("nerdctl pull localhost:5000/custom-golang-nginx:latest");

let container = nerdctl_container_from_image("golang-nginx-demo", local_image_name)
    .with_detach(true)
    .with_port("8080:80")  // Map port 80 in the container to 8080 on the host
    .with_restart_policy("unless-stopped")
    .build();

// Start the container
let start_result = container.start();

println("\nWorkflow completed successfully!");
println("The web server should be running at http://localhost:8080");
println("You can check container logs with: nerdctl logs golang-nginx-demo");
println("To stop the container: nerdctl stop golang-nginx-demo");
println("To remove the container: nerdctl rm golang-nginx-demo");

"Buildah and nerdctl workflow completed successfully!"
