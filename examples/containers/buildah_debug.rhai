// buildah_debug.rhai
// Demonstrates using the debug flag on the buildah Builder

println("Starting buildah debug example...");

// Define image and container names
let base_image = "ubuntu:22.04";
let container_name = "debug-test-container";

println(`Creating container '${container_name}' from base image '${base_image}'...`);

// Create a new buildah container using the builder pattern
let builder = bah_new(container_name, base_image);

// Enable debug mode
println("Enabling debug mode...");
builder.debug_mode = true;

// Run a simple command to see debug output
println("Running a command with debug enabled...");
let result = builder.run("echo 'Hello from debug mode'");

// Disable debug mode
println("Disabling debug mode...");
builder.debug_mode = false;

// Run another command without debug
println("Running a command with debug disabled...");
let result2 = builder.run("echo 'Hello without debug'");

// Enable debug mode again
println("Enabling debug mode again...");
builder.debug_mode = true;

// Remove the container with debug enabled
println("Removing the container with debug enabled...");
builder.remove();

println("Debug example completed!");