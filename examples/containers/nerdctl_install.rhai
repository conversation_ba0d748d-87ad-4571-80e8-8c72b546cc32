


fn nerdctl_download(){
    let name="nerdctl";
    let url="https://github.com/containerd/nerdctl/releases/download/v2.0.4/nerdctl-2.0.4-linux-amd64.tar.gz";
    download(url,`/tmp/${name}`,20000);
    copy(`/tmp/${name}/*`,"/root/hero/bin/");
    delete(`/tmp/${name}`);
    
    let name="containerd";
    let url="https://github.com/containerd/containerd/releases/download/v2.0.4/containerd-2.0.4-linux-amd64.tar.gz";
    download(url,`/tmp/${name}`,20000);
    copy(`/tmp/${name}/bin/*`,"/root/hero/bin/");
    delete(`/tmp/${name}`);

    run("apt-get -y install buildah runc");

    let url="https://github.com/threefoldtech/rfs/releases/download/v2.0.6/rfs";
    download_file(url,`/tmp/rfs`,10000);
    chmod_exec("/tmp/rfs");
    mv(`/tmp/rfs`,"/root/hero/bin/");

}

fn ipfs_download(){
    let name="ipfs";
    let url="https://github.com/ipfs/kubo/releases/download/v0.34.1/kubo_v0.34.1_linux-amd64.tar.gz";
    download(url,`/tmp/${name}`,20);
    copy(`/tmp/${name}/kubo/ipfs`,"/root/hero/bin/ipfs");
    // delete(`/tmp/${name}`);
    

}




nerdctl_download();
// ipfs_download();

"done"
