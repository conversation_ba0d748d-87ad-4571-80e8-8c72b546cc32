// PostgreSQL Authentication Example
//
// This example demonstrates how to use the PostgreSQL client module with authentication:
// - Create a PostgreSQL configuration with authentication
// - Connect to PostgreSQL using the configuration
// - Perform basic operations
//
// Prerequisites:
// - PostgreSQL server must be running
// - You need to know the username and password for the PostgreSQL server

// Helper function to check if PostgreSQL is available
fn is_postgres_available() {
    try {
        // Try to execute a simple connection
        let connect_result = pg_connect();
        return connect_result;
    } catch(err) {
        print(`PostgreSQL connection error: ${err}`);
        return false;
    }
}

// Main function
fn main() {
    print("=== PostgreSQL Authentication Example ===");

    // Check if PostgreSQL is available
    let postgres_available = is_postgres_available();
    if !postgres_available {
        print("PostgreSQL server is not available. Please check your connection settings.");
        return;
    }

    print("✓ PostgreSQL server is available");

    // Step 1: Create a PostgreSQL configuration with authentication
    print("\n1. Creating PostgreSQL configuration with authentication...");
    
    // Replace these values with your actual PostgreSQL credentials
    let pg_host = "localhost";
    let pg_port = 5432;
    let pg_user = "postgres";
    let pg_password = "your_password_here"; // Replace with your actual password
    let pg_database = "postgres";
    
    // Create a configuration builder
    let config = pg_config_builder();
    
    // Configure the connection
    config = config.host(pg_host);
    config = config.port(pg_port);
    config = config.user(pg_user);
    config = config.password(pg_password);
    config = config.database(pg_database);
    
    // Build the connection string
    let connection_string = config.build_connection_string();
    print(`✓ Created PostgreSQL configuration with connection string: ${connection_string}`);
    
    // Step 2: Connect to PostgreSQL using the configuration
    print("\n2. Connecting to PostgreSQL with authentication...");
    
    try {
        let connect_result = pg_connect_with_config(config);
        if (connect_result) {
            print("✓ Successfully connected to PostgreSQL with authentication");
        } else {
            print("✗ Failed to connect to PostgreSQL with authentication");
            return;
        }
    } catch(err) {
        print(`✗ Error connecting to PostgreSQL: ${err}`);
        return;
    }
    
    // Step 3: Perform basic operations
    print("\n3. Performing basic operations...");
    
    // Create a test table
    let table_name = "auth_example_table";
    let create_table_query = `
        CREATE TABLE IF NOT EXISTS ${table_name} (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL,
            value INTEGER
        )
    `;
    
    try {
        let create_result = pg_execute(create_table_query);
        print(`✓ Successfully created table ${table_name}`);
    } catch(err) {
        print(`✗ Error creating table: ${err}`);
        return;
    }
    
    // Insert data
    let insert_query = `
        INSERT INTO ${table_name} (name, value)
        VALUES ('test_name', 42)
    `;
    
    try {
        let insert_result = pg_execute(insert_query);
        print(`✓ Successfully inserted data into table ${table_name}`);
    } catch(err) {
        print(`✗ Error inserting data: ${err}`);
    }
    
    // Query data
    let select_query = `
        SELECT * FROM ${table_name}
    `;
    
    try {
        let select_result = pg_query(select_query);
        print(`✓ Successfully queried data from table ${table_name}`);
        print(`  Found ${select_result.len()} rows`);
        
        // Display the results
        for row in select_result {
            print(`  Row: id=${row.id}, name=${row.name}, value=${row.value}`);
        }
    } catch(err) {
        print(`✗ Error querying data: ${err}`);
    }
    
    // Clean up
    let drop_query = `
        DROP TABLE IF EXISTS ${table_name}
    `;
    
    try {
        let drop_result = pg_execute(drop_query);
        print(`✓ Successfully dropped table ${table_name}`);
    } catch(err) {
        print(`✗ Error dropping table: ${err}`);
    }
    
    print("\nExample completed successfully!");
}

// Run the main function
main();
