print("Getting a single process using process_get()...\n");

// process_get expects *exactly one* process matching the pattern.
// If zero or more than one processes match, it will halt script execution.

// Example: Get information for a specific process name.
// Replace "my_critical_service" with a name that is likely to match
// exactly one running process on your system.
// Common examples might be "Dock" or "Finder" on macOS,
// "explorer.exe" on Windows, or a specific service name on Linux.
let target_process_name = "process_name_to_get"; // <--- <PERSON><PERSON>GE THIS TO A REAL, UNIQUE PROCESS NAME

print(`Attempting to get info for process matching pattern: '${target_process_name}'...`);

// This line will halt if the process is not found OR if multiple processes match the name.
// It will only proceed if exactly one process is found.
let service_proc_info = process_get(target_process_name); // Halts on 0 or >1 matches, or OS error

print(`Successfully found exactly one process matching '${target_process_name}':`);

// Access properties of the ProcessInfo object
print(`- PID: ${service_proc_info.pid}`);
print(`- Name: ${service_proc_info.name}`);
print(`- CPU: ${service_proc_info.cpu}%`);
print(`- Memory: ${service_proc_info.memory}`);


// To demonstrate the halting behavior, you could uncomment one of these:

// Example that will halt if "nonexistent_process_xyz" is not running:
// print("\nAttempting to get a nonexistent process (will halt if not found)...");
// let nonexistent_proc = process_get("nonexistent_process_xyz"); // This line likely halts

// Example that might halt if "sh" matches multiple processes:
// print("\nAttempting to get 'sh' (might halt if multiple shell processes exist)...");
// let sh_proc = process_get("sh"); // This line might halt depending on your system processes


print("\nprocess_get() example finished (if the script did not halt above).");