print("Running a command using multiple builder options...");

// Example combining log, silent, and ignore_error
// This command will:
// 1. Be logged before execution (.log())
// 2. Have its output suppressed during execution (.silent())
// 3. Exit with a non-zero code (fail)
// 4. NOT halt the script execution because .ignore_error() is used
let result = run("echo 'This is logged and silent stdout'; echo 'This is logged and silent stderr' >&2; exit 5")
    .log()           // Log the command string
    .silent()        // Suppress real-time output
    .ignore_error()  // Prevent script halt on non-zero exit code
    .execute();           // Execute the command

print("Command execution finished.");

// Print the captured result
print(`Success: ${result.success}`);     // Should be false
print(`Exit Code: ${result.code}`);       // Should be 5
print(`Captured Stdout:\n${result.stdout}`); // Should contain the stdout string


// The script continues execution because ignore_error() was used
print("<PERSON><PERSON><PERSON> continues after handling the failed command.");

// Another example with a successful command, still silent and logged
print("\nRunning another command (successful)...");
let success_result = run("echo 'Success message'").log().silent().execute();
print(`Command finished.`);
print(`Success: ${success_result.success}`);   // Should be true
print(`Exit Code: ${success_result.code}`);     // Should be 0
print(`Captured Stdout:\n${success_result.stdout}`);



print("\nrun().execute() all options example finished.");