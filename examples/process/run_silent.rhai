print("Running a command using run().silent().do()...\n");

// This command will print to standard output and standard error
// However, because .silent() is used, the output will not appear in the console directly
let result = run("echo 'This should be silent stdout.'; echo 'This should be silent stderr.' >&2; exit 0").silent().do();

// The output is still captured in the CommandResult
print(`Command finished.`);
print(`Success: ${result.success}`);
print(`Exit Code: ${result.code}`);
print(`Captured Stdout:\\n${result.stdout}`);
print(`Captured Stderr:\\n${result.stderr}`);

// Example of a silent command that fails (but won't halt because we only suppress output)
// let fail_result = run("echo 'This is silent failure stderr.' >&2; exit 1").silent().do();
// print(`Failed command finished (silent):`);
// print(`Success: ${fail_result.success}`);
// print(`Exit Code: ${fail_result.code}`);
// print(`Captured Stdout:\\n${fail_result.stdout}`);
// print(`Captured Stderr:\\n${fail_result.stderr}`);

print("\nrun().silent() example finished.");