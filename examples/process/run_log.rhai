print("Running a command using run().log().do()...");

// The .log() method will print the command string to the console before execution.
// This is useful for debugging or tracing which commands are being run.
let result = run("echo This command is logged").log().do();

print(`Command finished.`);
print(`Success: ${result.success}`);
print(`Exit Code: ${result.code}`);
print(`Stdout:\n${result.stdout}`);
print(`Stderr:\n${result.stderr}`);

print("run().log() example finished.");