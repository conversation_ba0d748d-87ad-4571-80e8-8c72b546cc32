print("Running a basic command using run().do()...");

// Execute a simple command
let result = run("echo Hello from run_basic!").do();

// Print the command result
print(`Command: echo Hello from run_basic!`);
print(`Success: ${result.success}`);
print(`Exit Code: ${result.code}`);
print(`Stdout:\n${result.stdout}`);
print(`Stderr:\n${result.stderr}`);

// Example of a command that might fail (if 'nonexistent_command' doesn't exist)
// This will halt execution by default because ignore_error() is not used.
// print("Running a command that will fail (and should halt)...");
// let fail_result = run("nonexistent_command").do(); // This line will cause the script to halt if the command doesn't exist

print("Basic run() example finished.");