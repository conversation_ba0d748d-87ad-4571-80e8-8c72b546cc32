print("Running a command that will fail, but ignoring the error...");

// Run a command that exits with a non-zero code (will fail)
// Using .ignore_error() prevents the script from halting
let result = run("exit 1").ignore_error().do();

print(`Command finished.`);
print(`Success: ${result.success}`); // This should be false
print(`Exit Code: ${result.code}`);   // This should be 1

// We can now handle the failure in the script
if (!result.success) {
    print("Command failed, but we handled it because ignore_error() was used.");
    // Optionally print stderr if needed
    // print(`Stderr:\\n${result.stderr}`);
} else {
    print("Command unexpectedly succeeded.");
}

print("\nScript continued execution after the potentially failing command.");

// Example of a command that might fail due to OS error (e.g., command not found)
// This *might* still halt depending on how the underlying Rust function handles it,
// as ignore_error() primarily prevents halting on *command* non-zero exit codes.
// let os_error_result = run("nonexistent_command_123").ignore_error().do();
// print(`OS Error Command Success: ${os_error_result.success}`);
// print(`OS Error Command Exit Code: ${os_error_result.code}`);

print("ignore_error() example finished.");