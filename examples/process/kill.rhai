print("Caution: Use the kill() function with extreme care as it can terminate running applications.");
print("Terminating essential system processes can make your system unstable or unusable.");
print("");

print("This example attempts to kill processes matching a specific name.");
print("Replace 'process_name_to_kill' with the actual name of a process you intend to stop.");
print("Make sure you know what the process does before attempting to kill it.");
print("");

let target_process_name = "process_name_to_kill"; // <--- CH<PERSON><PERSON> THIS TO A REAL PROCESS NAME (e.g., "sleep" if you start a sleep process)

print(`Attempting to kill processes matching pattern: '${target_process_name}'...`);

// To safely test this, you might want to start a simple process first, like 'sleep 60 &'.
// Then replace 'process_name_to_kill' with 'sleep'.

// Uncomment the line below to execute the kill command.
// let result_message = kill(target_process_name); // Halts on OS error during kill attempt

// if result_message != "" {
//     print(`Kill command sent. Result: ${result_message}`);
// } else {
//     print("Kill command finished, but no message returned (check for errors above).");
// }

print("");
print("kill() example finished (command was commented out for safety).");
print("Uncomment the 'kill(...)' line to make it active.");