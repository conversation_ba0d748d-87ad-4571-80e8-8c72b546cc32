print("Checking if a command exists in the system PATH using which()...\n");

// Check for a command that likely exists (e.g., 'node' or 'git')
let command_name_exists = "node";
let command_path_exists = which(command_name_exists);

if (command_path_exists != "") {
    print(`'${command_name_exists}' executable found at: ${command_path_exists}`);
} else {
    print(`'${command_name_exists}' executable not found in PATH.`);
}

print("\nChecking for a command that likely does NOT exist...");

// Check for a command that likely does not exist
let command_name_nonexistent = "nonexistent_command_abc_123";
let command_path_nonexistent = which(command_name_nonexistent);

if (command_path_nonexistent != "") {
    print(`'${command_name_nonexistent}' executable found at: ${command_path_nonexistent}`);
} else {
    print(`'${command_name_nonexistent}' executable not found in PATH.`);
}

print("\nwhich() example finished.");