print("Listing processes using process_list()...\n");

// Example: List all processes (use empty string as pattern)
// print("Listing all running processes (this might be a long list!)...\n");
// let all_processes = process_list("");
// print(`Found ${all_processes.len()} total processes.`);
// // Optional: print details for a few processes
// for i in 0..min(all_processes.len(), 5) {
//     let proc = all_processes[i];
//     print(`- PID: ${proc.pid}, Name: ${proc.name}, CPU: ${proc.cpu}%, Memory: ${proc.memory}`);
// }

print("Listing processes matching 'bash'...\n");

// Example: List processes matching a pattern
let pattern_to_list = "bash"; // Or another common process like "SystemSettings" or "Finder" on macOS, "explorer.exe" on Windows, "systemd" on Linux
let matching_processes = process_list(pattern_to_list); // Halts on OS error during list attempt

if (matching_processes.len() > 0) {
    print(`Found ${matching_processes.len()} processes matching '${pattern_to_list}':`);
    for proc in matching_processes {
        // Access properties of the ProcessInfo object
        print(`- PID: ${proc.pid}, Name: ${proc.name}, CPU: ${proc.cpu}%, Memory: ${proc.memory}`);
    }
} else {
    print(`No processes found matching '${pattern_to_list}'.`);
}

print("\nprocess_list() example finished.");