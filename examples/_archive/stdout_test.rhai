

// Create a bash script to set up the test environment
let setup_script = `
# Configure git to suppress the default branch name warning
git config --global advice.initDefaultBranch false

rm -rf /tmp/code
mkdir -p /tmp/code
cd /tmp/code

mkdir -p myserver.com/myaccount/repogreen
mkdir -p myserver.com/myaccount/repored

cd myserver.com/myaccount/repogreen
git init
echo 'Initial test file' > test.txt
git add test.txt
git config --local user.email '<EMAIL>'
git config --local user.name 'Test User'
git commit -m 'Initial commit'

cd /tmp/code/myserver.com/myaccount/repored
git init
echo 'Initial test file' > test2.txt
git add test2.txt
git config --local user.email '<EMAIL>'
git config --local user.name 'Test User'
git commit -m 'Initial commit'

# now we have 2 repos

`;

// Run the setup script
let result = run(setup_script);
