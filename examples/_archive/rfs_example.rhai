// RFS Example Script
// This script demonstrates how to use the RFS wrapper in Rhai

// Mount a local directory
fn mount_local_example() {
    print("Mounting a local directory...");
    
    // Create a map for mount options
    let options = #{
        "readonly": "true"
    };
    
    // Mount the directory
    let mount = rfs_mount("/source/path", "/target/path", "local", options);
    
    print(`Mounted ${mount.source} to ${mount.target} with ID: ${mount.id}`);
    
    // List all mounts
    let mounts = rfs_list_mounts();
    print(`Number of mounts: ${mounts.len()}`);
    
    for mount in mounts {
        print(`Mount ID: ${mount.id}, Source: ${mount.source}, Target: ${mount.target}`);
    }
    
    // Unmount the directory
    rfs_unmount("/target/path");
    print("Unmounted the directory");
}

// Pack a directory into a filesystem layer
fn pack_example() {
    print("Packing a directory into a filesystem layer...");
    
    // Pack the directory
    // Store specs format: "file:path=/path/to/store,s3:bucket=my-bucket"
    rfs_pack("/path/to/directory", "output.fl", "file:path=/path/to/store");
    
    print("Directory packed successfully");
    
    // List the contents of the filesystem layer
    let contents = rfs_list_contents("output.fl");
    print("Contents of the filesystem layer:");
    print(contents);
    
    // Verify the filesystem layer
    let is_valid = rfs_verify("output.fl");
    print(`Is the filesystem layer valid? ${is_valid}`);
    
    // Unpack the filesystem layer
    rfs_unpack("output.fl", "/path/to/unpack");
    print("Filesystem layer unpacked successfully");
}

// SSH mount example
fn mount_ssh_example() {
    print("Mounting a remote directory via SSH...");
    
    // Create a map for mount options
    let options = #{
        "port": "22",
        "identity_file": "/path/to/key",
        "readonly": "true"
    };
    
    // Mount the directory
    let mount = rfs_mount("<EMAIL>:/remote/path", "/local/mount/point", "ssh", options);
    
    print(`Mounted ${mount.source} to ${mount.target} with ID: ${mount.id}`);
    
    // Get mount info
    let info = rfs_get_mount_info("/local/mount/point");
    print(`Mount info: ${info}`);
    
    // Unmount the directory
    rfs_unmount("/local/mount/point");
    print("Unmounted the directory");
}

// S3 mount example
fn mount_s3_example() {
    print("Mounting an S3 bucket...");
    
    // Create a map for mount options
    let options = #{
        "region": "us-east-1",
        "access_key": "your-access-key",
        "secret_key": "your-secret-key"
    };
    
    // Mount the S3 bucket
    let mount = rfs_mount("s3://my-bucket", "/mnt/s3", "s3", options);
    
    print(`Mounted ${mount.source} to ${mount.target} with ID: ${mount.id}`);
    
    // Unmount the S3 bucket
    rfs_unmount("/mnt/s3");
    print("Unmounted the S3 bucket");
}

// Unmount all example
fn unmount_all_example() {
    print("Unmounting all filesystems...");
    
    // Unmount all filesystems
    rfs_unmount_all();
    
    print("All filesystems unmounted");
}

// Run the examples
// Note: These are commented out to prevent accidental execution
// Uncomment the ones you want to run

// mount_local_example();
// pack_example();
// mount_ssh_example();
// mount_s3_example();
// unmount_all_example();

print("RFS example script completed");