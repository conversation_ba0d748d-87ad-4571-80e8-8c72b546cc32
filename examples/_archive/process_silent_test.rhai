// Test script for run_silent functionality

print("===== Testing run_silent functionality =====");

// Helper function for assertions
fn assert(condition, message) {
    if (condition == false) {
        print(`FAILED: ${message}`);
        throw `Assertion failed: ${message}`;
    } else {
        print(`PASSED: ${message}`);
    }
}

// Test 1: Basic run_silent with a successful command
print("\n=== Test 1: Basic run_silent with successful command ===");
let silent_result = run_silent("echo This output should not be visible");
print("Result from silent echo command:");
print(`  success: ${silent_result.success}`);
print(`  code: ${silent_result.code}`);
print(`  stdout length: ${silent_result.stdout.len()}`);
print(`  stderr length: ${silent_result.stderr.len()}`);

// Assert that the command succeeded
assert(silent_result.success, "Silent command should succeed");
assert(silent_result.code.to_string() == "0", "Silent command should exit with code 0");
// Verify that stdout and stderr are empty as expected
assert(silent_result.stdout == "", "Silent command stdout should be empty");
assert(silent_result.stderr == "", "Silent command stderr should be empty");

// Test 2: Compare with regular run function
print("\n=== Test 2: Compare with regular run function ===");
let normal_result = run("echo This output should be visible");
print("Result from normal echo command:");
print(`  success: ${normal_result.success}`);
print(`  code: ${normal_result.code}`);
print(`  stdout: "${normal_result.stdout.trim()}"`);
print(`  stderr length: ${normal_result.stderr.len()}`);

// Assert that the command succeeded
assert(normal_result.success, "Normal command should succeed");
assert(normal_result.code.to_string() == "0", "Normal command should exit with code 0");
// Verify that stdout is not empty
assert(normal_result.stdout != "", "Normal command stdout should not be empty");
assert(normal_result.stdout.contains("visible"), "Normal command stdout should contain our message");

// Test 3: run_silent with a failing command
print("\n=== Test 3: run_silent with a failing command ===");
let silent_fail = run_silent("ls /directory_that_does_not_exist");
print("Result from silent failing command:");
print(`  success: ${silent_fail.success}`);
print(`  code: ${silent_fail.code}`);
print(`  stdout length: ${silent_fail.stdout.len()}`);
print(`  stderr length: ${silent_fail.stderr.len()}`);

// Assert that the command failed but didn't throw an error
assert(silent_fail.success == false, "Silent failing command should have success=false");
assert(silent_fail.code.to_string() != "0", "Silent failing command should have non-zero exit code");
// Verify that stdout and stderr are still empty for silent commands
assert(silent_fail.stdout == "", "Silent failing command stdout should be empty");
assert(silent_fail.stderr == "", "Silent failing command stderr should be empty");

// Test 4: Normal run with a failing command
print("\n=== Test 4: Normal run with a failing command ===");
let normal_fail = run("ls /directory_that_does_not_exist");
print("Result from normal failing command:");
print(`  success: ${normal_fail.success}`);
print(`  code: ${normal_fail.code}`);
print(`  stdout length: ${normal_fail.stdout.len()}`);
print(`  stderr length: ${normal_fail.stderr.len()}`);

// Assert that the command failed
assert(normal_fail.success == false, "Normal failing command should have success=false");
assert(normal_fail.code.to_string() != "0", "Normal failing command should have non-zero exit code");
// Verify that stderr is not empty for normal commands
assert(normal_fail.stderr != "", "Normal failing command stderr should not be empty");

print("\n===== All run_silent tests passed! =====");

"run_silent function works correctly"
