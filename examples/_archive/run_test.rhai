// Test script for the run command functionality

print("===== Run Command Test =====");

// Test single command
print("\n=== Single Command Execution ===");
let result = run("echo Hello, World!");
print(`Command stdout: ${result.stdout}`);
print(`Command stderr: ${result.stderr}`);
print(`Command success: ${result.success}`);
print(`Command exit code: ${result.code}`);

// Test command with arguments
print("\n=== Command With Arguments ===");
let ls_result = run("ls -la /tmp");
// Use string truncation by direct manipulation instead of substr
let ls_output = if ls_result.stdout.len() > 100 {
    ls_result.stdout[0..100] + "..."
} else {
    ls_result.stdout
};
print(`ls -la /tmp stdout: ${ls_output}`);
print(`ls success: ${ls_result.success}`);

// Test command that doesn't exist
print("\n=== Non-existent Command ===");
let bad_result = run("command_that_doesnt_exist");
print(`Bad command success: ${bad_result.success}`);
print(`Bad command error: ${bad_result.stderr}`);

// Test command with environment variables
print("\n=== Command With Environment Variables ===");
let home_result = run("echo $HOME");
print(`Home directory: ${home_result.stdout}`);

// Test multiline script
print("\n=== Multiline Script Execution ===");
let script = `
    # This is a multiline script
    echo "Line 1"
    echo "Line 2"
    echo "Line 3"
    
    # Show the date
    date
    
    # List files in current directory
    ls -la | head -n 5
`;

print("Executing multiline script:");
let script_result = run(script);
print("Script output:");
print(script_result.stdout);

// Test script with indentation (to test dedenting)
print("\n=== Indented Script (Testing Dedent) ===");
let indented_script = `
    # This script has extra indentation
        echo "This line has extra indentation"
        echo "This line also has extra indentation"
    echo "This line has normal indentation"
`;

print("Executing indented script:");
let indented_result = run(indented_script);
print("Indented script output:");
print(indented_result.stdout);

print("\n===== Run Command Test Completed =====");

"Success"
