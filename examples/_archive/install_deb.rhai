
fn dragonfly(){
    download("https://github.com/dragonflyoss/dragonfly/releases/download/v2.2.1/dragonfly-2.2.1-linux-amd64.tar.gz", "/tmp/dragonfly", 55000);
    copy("/tmp/dragonfly","/root/hero/bin");
    delete("/tmp/dragonfly");
}

fn nydus(){
    let url="https://github.com/dragonflyoss/nydus/releases/download/v2.3.1/nydus-static-v2.3.1-linux-amd64.tgz";
    download(url,"/tmp/nydus",20);
    copy("/tmp/nydus/nydus-static/*","/root/hero/bin/");
    delete("/tmp/nydus");
}

fn nerdctl(){
    let name="nerctl"
    let url="https://github.com/containerd/nerdctl/releases/download/v2.0.4/nerdctl-2.0.4-linux-amd64.tar.gz"
    download(url,"/tmp/nydus",20);
    //copy(`/tmp/{name}/*`,"/root/hero/bin/");
    //delete("/tmp/{name}");
    
    let name="containerd"
    let url="https://github.com/containerd/containerd/releases/download/v2.0.4/containerd-2.0.4-linux-amd64.tar.gz";
    download(url,"/tmp/nydus",20);
    //copy(`/tmp/{name}/*`,"/root/hero/bin/");
    //delete("/tmp/{name}");

}

nydus();

"done"
