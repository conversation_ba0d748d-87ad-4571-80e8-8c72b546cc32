// Comprehensive file system operations test script with assertions

print("===== File System Operations Test =====");

// Helper functions for testing
fn assert(condition, message) {
    if (condition == false) {
        print(`FAILED: ${message}`);
        throw `Assertion failed: ${message}`;
    } else {
        print(`PASSED: ${message}`);
    }
}

fn assert_equal(actual, expected, message) {
    // Convert numbers to strings before comparison to avoid type issues
    let actual_str = actual.to_string();
    let expected_str = expected.to_string();
    
    if (actual_str != expected_str) {
        print(`FAILED: ${message} - Expected '${expected}', got '${actual}'`);
        throw `Assertion failed: ${message}`;
    } else {
        print(`PASSED: ${message}`);
    }
}

fn assert_true(value, message) {
    assert(value, message);
}

fn assert_false(value, message) {
    assert(value == false, message);
}

// Directory for tests
let test_dir = "/tmp/herodo_test_fs";
let tests_total = 0;

// Setup - create test directory
print("\n=== Setup ===");
if exist(test_dir) {
    print(`Test directory exists, removing it first...`);
    let result = delete(test_dir);
    // Function will throw an error if it fails
    assert_false(exist(test_dir), "Test directory should not exist after deletion");
}

// Test mkdir
print("\n=== Test mkdir() ===");
print(`Creating test directory: ${test_dir}`);
tests_total += 1;
let mkdir_result = mkdir(test_dir);
// Now can directly use the returned success message
assert_true(exist(test_dir), "Test directory should exist after creation");

// Test mkdir with nested paths
print(`Creating nested directory: ${test_dir}/subdir/nested`);
tests_total += 1;
let nested_result = mkdir(`${test_dir}/subdir/nested`);
assert_true(exist(`${test_dir}/subdir/nested`), "Nested directory should exist after creation");

// Test duplicate mkdir (should not error)
print(`Creating existing directory again: ${test_dir}`);
tests_total += 1;
let duplicate_result = mkdir(test_dir);
// This should just return a message that directory already exists

// Test file creation using run
print("\n=== Test file creation ===");
let file1 = `${test_dir}/file1.txt`;
let file2 = `${test_dir}/file2.txt`;
let file3 = `${test_dir}/subdir/file3.txt`;

// Create files
print(`Creating test files...`);
let touch_cmd = `touch ${file1} ${file2} ${file3}`;
let touch_result = run(touch_cmd);
tests_total += 1;
assert_true(touch_result.success, "File creation using touch should succeed");

// Verify files exist
print(`Verifying files exist...`);
tests_total += 1;
assert_true(exist(file1), "File 1 should exist after creation");
assert_true(exist(file2), "File 2 should exist after creation");
assert_true(exist(file3), "File 3 should exist after creation");
print("All test files were created successfully");

// Test copy
print("\n=== Test copy() ===");
let copy_file = `${test_dir}/file1_copy.txt`;
print(`Copying ${file1} to ${copy_file}`);
tests_total += 1;
let copy_result = copy(file1, copy_file);
tests_total += 1;
assert_true(exist(copy_file), "Copied file should exist");

// Test directory copy
print(`Copying directory ${test_dir}/subdir to ${test_dir}/subdir_copy`);
tests_total += 1;
let dir_copy_result = copy(`${test_dir}/subdir`, `${test_dir}/subdir_copy`);
tests_total += 1;
assert_true(exist(`${test_dir}/subdir_copy`), "Copied directory should exist");
tests_total += 1;
assert_true(exist(`${test_dir}/subdir_copy/file3.txt`), "Files in copied directory should exist");

// Test file searching
print("\n=== Test find_file() and find_files() ===");

// Create log files for testing search
print("Creating log files for testing search...");
let log_file1 = `${test_dir}/subdir/test1.log`;
let log_file2 = `${test_dir}/subdir/test2.log`;
let log_file3 = `${test_dir}/subdir_copy/test3.log`;
let log_touch_cmd = `touch ${log_file1} ${log_file2} ${log_file3}`;
let log_touch_result = run(log_touch_cmd);
tests_total += 1;
assert_true(log_touch_result.success, "Log file creation should succeed");

// Verify log files exist
print("Verifying log files exist...");
assert_true(exist(log_file1), "Log file 1 should exist after creation");
assert_true(exist(log_file2), "Log file 2 should exist after creation");
assert_true(exist(log_file3), "Log file 3 should exist after creation");
print("All log files were created successfully");

// Test find_file
print("Testing find_file for a single file:");
let found_file = find_file(test_dir, "file1.txt");
tests_total += 1;
assert_true(found_file.to_string().contains("file1.txt"), "find_file should find the correct file");

// Test find_file with wildcard
print("Testing find_file with wildcard:");
let log_file = find_file(test_dir, "*.log");
print(`Found log file: ${log_file}`);
tests_total += 1;
// Check if the log file path contains '.log'
let is_log_file = log_file.to_string().contains(".log");
assert_true(is_log_file, "find_file should find a log file");

// Test find_files
print("Testing find_files with wildcard:");
let log_files = find_files(test_dir, "*.log");
print(`Found ${log_files.len()} log files with find_files`);
tests_total += 1;
assert_equal(log_files.len(), 3, "find_files should find all 3 log files");

// Test find_dir
print("\n=== Test find_dir() and find_dirs() ===");
let found_dir = find_dir(test_dir, "subdir");
tests_total += 1;
assert_true(found_dir.to_string().contains("subdir"), "find_dir should find the correct directory");

// Test find_dirs
let all_dirs = find_dirs(test_dir, "*dir*");
tests_total += 1;
assert_equal(all_dirs.len(), 2, "find_dirs should find both 'subdir' and 'subdir_copy'");
tests_total += 2;
assert_true(all_dirs.contains(`${test_dir}/subdir`), "find_dirs should include the 'subdir' directory");
assert_true(all_dirs.contains(`${test_dir}/subdir_copy`), "find_dirs should include the 'subdir_copy' directory");

// Test sync by manually copying instead of rsync
print("\n=== Test sync() ===");
print(`Copying directory ${test_dir}/subdir to ${test_dir}/sync_target`);
tests_total += 1;
let sync_result = copy(`${test_dir}/subdir`, `${test_dir}/sync_target`);
tests_total += 1;
assert_true(exist(`${test_dir}/sync_target`), "Sync target directory should exist");

// Create test files in sync target to verify they exist
print("Creating test files in sync target...");
let sync_file1 = `${test_dir}/sync_target/sync_test1.log`;
let sync_file2 = `${test_dir}/sync_target/sync_test2.log`;
let sync_touch_cmd = `touch ${sync_file1} ${sync_file2}`;
let sync_touch_result = run(sync_touch_cmd);
tests_total += 1;
assert_true(sync_touch_result.success, "Creating test files in sync target should succeed");
tests_total += 1;
assert_true(exist(sync_file1), "Test files should exist in sync target");

// Test delete
print("\n=== Test delete() ===");
print(`Deleting file: ${copy_file}`);
tests_total += 1;
let delete_file_result = delete(copy_file);
tests_total += 1;
assert_false(exist(copy_file), "File should not exist after deletion");

// Test delete non-existent file (should be defensive)
print(`Deleting non-existent file:`);
tests_total += 1;
let nonexistent_result = delete(`${test_dir}/nonexistent.txt`);
// This should not throw an error, just inform no file was deleted

// Test delete directory
print(`Deleting directory: ${test_dir}/subdir_copy`);
tests_total += 1;
let dir_delete_result = delete(`${test_dir}/subdir_copy`);
tests_total += 1;
assert_false(exist(`${test_dir}/subdir_copy`), "Directory should not exist after deletion");

// Cleanup
print("\n=== Cleanup ===");
print(`Removing test directory: ${test_dir}`);
tests_total += 1;
let cleanup_result = delete(test_dir);
tests_total += 1;
assert_false(exist(test_dir), "Test directory should not exist after cleanup");

// Test summary
print("\n===== Test Summary =====");
print(`Total tests run: ${tests_total}`);
print(`All tests passed!`);

"File System Test Success - All tests passed"
