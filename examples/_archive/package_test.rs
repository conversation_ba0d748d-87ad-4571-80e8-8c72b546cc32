//! Example of using the package management module
//!
//! This example demonstrates how to use the package management module
//! to install, remove, and manage packages on different platforms.

use sal::os::package::{PackHero, Platform};

fn main() {
    // Create a new PackHero instance
    let mut hero = PackHero::new();
    
    // Enable debug output
    hero.set_debug(true);
    
    // Detect the platform
    let platform = hero.platform();
    println!("Detected platform: {:?}", platform);
    
    // Only proceed if we're on a supported platform
    if platform == Platform::Unknown {
        println!("Unsupported platform. This example only works on Ubuntu and macOS.");
        return;
    }
    
    // Test package to install/check
    let test_package = if platform == Platform::Ubuntu { "wget" } else { "wget" };
    
    // Check if the package is installed
    match hero.is_installed(test_package) {
        Ok(is_installed) => {
            println!("Package {} is installed: {}", test_package, is_installed);
            
            if is_installed {
                println!("Package {} is already installed", test_package);
            } else {
                println!("Package {} is not installed, attempting to install...", test_package);
                
                // Try to install the package
                match hero.install(test_package) {
                    Ok(_) => println!("Successfully installed package {}", test_package),
                    Err(e) => println!("Failed to install package {}: {}", test_package, e),
                }
                
                // Check if it was installed successfully
                match hero.is_installed(test_package) {
                    Ok(is_installed_now) => {
                        if is_installed_now {
                            println!("Verified package {} was installed successfully", test_package);
                        } else {
                            println!("Package {} was not installed successfully", test_package);
                        }
                    },
                    Err(e) => println!("Error checking if package is installed: {}", e),
                }
            }
        },
        Err(e) => println!("Error checking if package is installed: {}", e),
    }
    
    // Search for packages
    let search_term = "wget";
    println!("Searching for packages with term '{}'...", search_term);
    match hero.search(search_term) {
        Ok(results) => {
            println!("Found {} packages matching '{}'", results.len(), search_term);
            for (i, package) in results.iter().enumerate().take(5) {
                println!("  {}. {}", i + 1, package);
            }
            if results.len() > 5 {
                println!("  ... and {} more", results.len() - 5);
            }
        },
        Err(e) => println!("Error searching for packages: {}", e),
    }
    
    // List installed packages
    println!("Listing installed packages...");
    match hero.list_installed() {
        Ok(packages) => {
            println!("Found {} installed packages", packages.len());
            println!("First 5 installed packages:");
            for (i, package) in packages.iter().enumerate().take(5) {
                println!("  {}. {}", i + 1, package);
            }
            if packages.len() > 5 {
                println!("  ... and {} more", packages.len() - 5);
            }
        },
        Err(e) => println!("Error listing installed packages: {}", e),
    }
    
    // Update package lists
    println!("Updating package lists...");
    match hero.update() {
        Ok(_) => println!("Successfully updated package lists"),
        Err(e) => println!("Error updating package lists: {}", e),
    }
    
    println!("Package management example completed");
}