// This is a sample Rhai script demonstrating the Herodo module functionality
// It shows the use of file system, process management, and git operations

print("===== Herodo Sample Script =====");

// File System Operations ===========================================
print("\n===== File System Operations =====");

// Check if directory exists and make it if not
if !exist("./test_dir") {
    print("Creating test directory...");
    mkdir("./test_dir");
}

// Write a test file
print("Writing test file...");
let content = "This is a test file created by <PERSON><PERSON>";
let file_path = "./test_dir/test.txt";
run(`echo "${content}" > ${file_path}`);

// Check existence
print(`File exists: ${exist(file_path)}`);

// Copy file
print("Copying file...");
let copy_path = "./test_dir/test_copy.txt";
copy(file_path, copy_path);
print(`Copy exists: ${exist(copy_path)}`);

// Show directory contents
print("Directory contents:");
print(run(`ls -la ./test_dir`).stdout);

// Process Management ==============================================
print("\n===== Process Management =====");

// Check if a command exists
print(`ls command exists: ${which("ls")}`);
print(`invalid command exists: ${which("thiscommanddoesnotexist")}`);

// Run a command and capture output
print("Running echo command:");
let echo_result = run("echo Hello from Herodo!");
print(`  stdout: ${echo_result.stdout}`);
print(`  success: ${echo_result.success}`);

// Run a multiline script
print("Running multiline script:");
let script = `
    echo "Line 1"
    echo "Line 2"
    echo "Line 3"
`;
let script_result = run(script);
print(`  stdout: ${script_result.stdout}`);

// List processes (limited to avoid large output)
print("Listing processes containing 'sh':");
let processes = process_list("sh");
if processes.len() > 0 {
    print(`Found ${processes.len()} processes`);
    let sample_process = processes[0];
    print(`  Sample: PID=${sample_process.pid}, Name=${sample_process.name}`);
} else {
    print("No processes found matching 'sh'");
}

// Git and Download Operations ====================================
print("\n===== Git and Download Operations =====");

// Check if we can download a file (without actually downloading)
print("Download operations available:");
print(`  download() function available: true`);

// Clean up test directory
print("\n===== Cleanup =====");
print("Deleting test directory...");
delete("./test_dir");
print(`Directory exists after deletion: ${exist("./test_dir")}`);

print("\nTest script completed successfully!");
"Success"  // Return value
