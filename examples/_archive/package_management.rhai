// Example script demonstrating the mypackage management functions

// Set debug mode to true to see detailed output
package_set_debug(true);

// Function to demonstrate mypackage management on Ubuntu
fn demo_ubuntu() {
    print("Demonstrating mypackage management on Ubuntu...");
    
    // Update mypackage lists
    print("Updating mypackage lists...");
    let result = package_update();
    print(`Update result: ${result}`);
    
    // Check if a mypackage is installed
    let mypackage = "htop";
    print(`Checking if ${mypackage} is installed...`);
    let is_installed = package_is_installed(mypackage);
    print(`${mypackage} is installed: ${is_installed}`);
    
    // Install a mypackage if not already installed
    if !is_installed {
        print(`Installing ${mypackage}...`);
        let install_result = package_install(mypackage);
        print(`Install result: ${install_result}`);
    }
    
    // List installed packages (limited to first 5 for brevity)
    print("Listing installed packages (first 5)...");
    let packages = package_list();
    for i in 0..min(5, packages.len()) {
        print(`  - ${packages[i]}`);
    }
    
    // Search for packages
    let search_term = "editor";
    print(`Searching for packages with term '${search_term}'...`);
    let search_results = package_search(search_term);
    print(`Found ${search_results.len()} packages. First 5 results:`);
    for i in 0..min(5, search_results.len()) {
        print(`  - ${search_results[i]}`);
    }
    
    // Remove the mypackage if we installed it
    if !is_installed {
        print(`Removing ${mypackage}...`);
        let remove_result = package_remove(mypackage);
        print(`Remove result: ${remove_result}`);
    }
}

// Function to demonstrate mypackage management on macOS
fn demo_macos() {
    print("Demonstrating mypackage management on macOS...");
    
    // Update mypackage lists
    print("Updating mypackage lists...");
    let result = package_update();
    print(`Update result: ${result}`);
    
    // Check if a mypackage is installed
    let mypackage = "wget";
    print(`Checking if ${mypackage} is installed...`);
    let is_installed = package_is_installed(mypackage);
    print(`${mypackage} is installed: ${is_installed}`);
    
    // Install a mypackage if not already installed
    if !is_installed {
        print(`Installing ${mypackage}...`);
        let install_result = package_install(mypackage);
        print(`Install result: ${install_result}`);
    }
    
    // List installed packages (limited to first 5 for brevity)
    print("Listing installed packages (first 5)...");
    let packages = package_list();
    for i in 0..min(5, packages.len()) {
        print(`  - ${packages[i]}`);
    }
    
    // Search for packages
    let search_term = "editor";
    print(`Searching for packages with term '${search_term}'...`);
    let search_results = package_search(search_term);
    print(`Found ${search_results.len()} packages. First 5 results:`);
    for i in 0..min(5, search_results.len()) {
        print(`  - ${search_results[i]}`);
    }
    
    // Remove the mypackage if we installed it
    if !is_installed {
        print(`Removing ${mypackage}...`);
        let remove_result = package_remove(mypackage);
        print(`Remove result: ${remove_result}`);
    }
}

// Detect platform and run the appropriate demo
fn main() {
    // Create a PackHero instance to detect the platform
    let platform = package_platform();
    
    if platform == "Ubuntu" {
        demo_ubuntu();
    } else if platform == "MacOS" {
        demo_macos();
    } else {
        print(`Unsupported platform: ${platform}`);
    }
}

// Run the main function
main();