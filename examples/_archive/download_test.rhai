
print("\n=== Test download() Functionality ===");

// Create test directory
let download_dir = "/tmp/downloadtest";

// Clean up any previous test files
delete(download_dir);
mkdir(download_dir);
print("Created test directory for downloads at " + download_dir);

// Test URLs
let zip_url = "https://github.com/freeflowuniverse/herolib/archive/refs/tags/v1.0.24.zip";
let targz_url = "https://github.com/freeflowuniverse/herolib/archive/refs/tags/v1.0.24.tar.gz";
let binary_url = "https://github.com/freeflowuniverse/herolib/releases/download/v1.0.24/hero-aarch64-unknown-linux-musl";

// Create destinations
let zip_dest = `${download_dir}/zip`;
let targz_dest = `${download_dir}/targz`;
let binary_dest = `${download_dir}/hero-binary`;


//PART 1

// Download and extract .zip file
print("\nTesting .zip download:");
// Download function now extracts zip files automatically
let result = download(zip_url, zip_dest, 0);

// Check if files were extracted
let file_count = find_files(zip_dest, "*").len();
print(`  Files found after extraction: ${file_count}`);
let success_msg = if file_count > 0 { "yes" } else { "no" };
print(`  Extraction successful: ${success_msg}`);

//PART 2

// Download and extract .tar.gz file
print("\nTesting .tar.gz download:");
let result = download(targz_url, targz_dest, 0);

// Check if files were extracted (download function should extract tar.gz automatically)
let file_count = find_files(targz_dest, "*").len();
print(`  Files found after extraction: ${file_count}`);
let success_msg = if file_count > 100 { "yes" } else { "no" };
print(`  Extraction successful: ${success_msg}`);

//PART 3

// Download binary file and check size
print("\nTesting binary download:");
download_file(binary_url, binary_dest, 8000);

// Check file size using our new file_size function
let size_bytes = file_size(binary_dest);
let size_mb = size_bytes / (1024 * 1024);
print(`  File size: ${size_mb} MB`);
let size_check = if size_mb > 5 { "yes" } else { "no" };
print(`  Size > 5MB: ${size_check}`);
let success_msg = if size_mb >= 8 > 100 { "yes" } else { "no" };
print(`  Minimum size check passed:${success_msg}`);

// Clean up test files
delete(download_dir);
print("Cleaned up test directory");
//PART 4

// Test the new download_file function
print("\nTesting download_file function:");
let text_url = "https://raw.githubusercontent.com/freeflowuniverse/herolib/main/README.md";
let text_file_dest = `${download_dir}/README.md`;

// Create the directory again for this test
mkdir(download_dir);

// Download a text file using the new download_file function
let file_result = download_file(text_url, text_file_dest, 0);
print(`  File downloaded to: ${file_result}`);

// Check if the file exists and has content
let file_exists = exist(text_file_dest);
print(`  File exists: ${file_exists}`);
let file_content = file_read(text_file_dest);
let content_check = if file_content.len() > 100 { "yes" } else { "no" };
print(`  File has content: ${content_check}`);

//PART 5

// Test the new chmod_exec function
print("\nTesting chmod_exec function:");
// Create a simple shell script
let script_path = `${download_dir}/test_script.sh`;
file_write(script_path, "#!/bin/sh\necho 'Hello from test script'");

// Make it executable
let chmod_result = chmod_exec(script_path);
print(`  ${chmod_result}`);

// Clean up test files again
delete(download_dir);
print("Cleaned up test directory");

print("\nAll Download Tests completed successfully!");
"Download Tests Success"
"Download Tests Success"
