// text_tools.rhai
// Example script demonstrating the text tools functionality

// ===== TextReplacer Examples =====
println("===== TextReplacer Examples =====");

// Create a temporary file for testing
let temp_file = "text_replacer_test.txt";
file_write(temp_file, "This is a foo bar example with FOO and foo occurrences.\nAnother line with foo and bar.");

// Example 1: Simple replacement
println("\n--- Example 1: Simple replacement ---");
let replacer = text_replacer_new()
    .pattern("foo")
    .replacement("REPLACED")
    .build();

let result = replacer.replace("foo bar foo");
println(`Result: ${result}`);  // Should output: "REPLACED bar REPLACED"

// Example 2: Multiple replacements in one chain
println("\n--- Example 2: Multiple replacements in one chain ---");
let replacer = text_replacer_new()
    .pattern("foo").replacement("AAA")
    .pattern("bar").replacement("BBB")
    .build();

let result = replacer.replace("foo bar foo baz");
println(`Result: ${result}`);  // Should output: "AAA BBB AAA baz"

// Example 3: Case-insensitive regex replacement
println("\n--- Example 3: Case-insensitive regex replacement ---");
let replacer = text_replacer_new()
    .pattern("foo")
    .replacement("case-insensitive")
    .regex(true)
    .case_insensitive(true)
    .build();

let result = replacer.replace("FOO foo Foo fOo");
println(`Result: ${result}`);  // Should output: "case-insensitive case-insensitive case-insensitive case-insensitive"

// Example 4: File operations
println("\n--- Example 4: File operations ---");
let replacer = text_replacer_new()
    .pattern("foo").replacement("EXAMPLE")
    .build();

// Replace and get result as string
let file_result = replacer.replace_file(temp_file);
println(`File content after replacement:\n${file_result}`);

// Replace in-place
replacer.replace_file_in_place(temp_file);
println("File replaced in-place");

// Replace to a new file
let output_file = "text_replacer_output.txt";
replacer.replace_file_to(temp_file, output_file);
println(`Content written to new file: ${output_file}`);

// Clean up temporary files
delete(temp_file);
delete(output_file);

// ===== TemplateBuilder Examples =====
println("\n\n===== TemplateBuilder Examples =====");

// Create a temporary template file
let template_file = "template_test.txt";
file_write(template_file, "Hello, {{ name }}! Welcome to {{ place }}.\n{% if show_greeting %}Glad to have you here!{% endif %}\nYour items:\n{% for item in items %}  - {{ item }}{% if not loop.last %}\n{% endif %}{% endfor %}\n");

// Example 1: Simple template rendering
println("\n--- Example 1: Simple template rendering ---");
let template = template_builder_open(template_file)
    .add_var("name", "John")
    .add_var("place", "Rhai")
    .add_var("show_greeting", true)
    .add_var("items", ["apple", "banana", "cherry"]);

let result = template.render();
println(`Rendered template:\n${result}`);

// Example 2: Using a map for variables
println("\n--- Example 2: Using a map for variables ---");
let vars = #{
    name: "Alice",
    place: "Template World"
};

let template = template_builder_open(template_file)
    .add_vars(vars)
    .add_var("show_greeting", false)
    .add_var("items", ["laptop", "phone", "tablet"]);

let result = template.render();
println(`Rendered template with map:\n${result}`);

// Example 3: Rendering to a file
println("\n--- Example 3: Rendering to a file ---");
let output_file = "template_output.txt";

let template = template_builder_open(template_file)
    .add_var("name", "Bob")
    .add_var("place", "File Output")
    .add_var("show_greeting", true)
    .add_var("items", ["document", "spreadsheet", "presentation"]);

template.render_to_file(output_file);
println(`Template rendered to file: ${output_file}`);
println(`Content of the rendered file:\n${file_read(output_file)}`);

// Clean up temporary files
delete(template_file);
delete(output_file);

// ===== Fix Functions Examples =====
println("\n\n===== Fix Functions Examples =====");

// Example 1: name_fix
println("\n--- Example 1: name_fix ---");
let fixed_name = name_fix("Hello World!");
println(`Original: "Hello World!"`);
println(`Fixed: "${fixed_name}"`);  // Should output: "hello_world"

let fixed_name = name_fix("File-Name.txt");
println(`Original: "File-Name.txt"`);
println(`Fixed: "${fixed_name}"`);  // Should output: "file_name.txt"

let fixed_name = name_fix("Résumé.doc");
println(`Original: "Résumé.doc"`);
println(`Fixed: "${fixed_name}"`);  // Should output: "rsum.doc"

// Example 2: path_fix
println("\n--- Example 2: path_fix ---");
let fixed_path = path_fix("/path/to/Hello World!");
println(`Original: "/path/to/Hello World!"`);
println(`Fixed: "${fixed_path}"`);  // Should output: "/path/to/hello_world"

let fixed_path = path_fix("./relative/path/to/DOCUMENT-123.pdf");
println(`Original: "./relative/path/to/DOCUMENT-123.pdf"`);
println(`Fixed: "${fixed_path}"`);  // Should output: "./relative/path/to/document_123.pdf"

// ===== Dedent Functions Examples =====
println("\n\n===== Dedent Functions Examples =====");

// Example 1: dedent
println("\n--- Example 1: dedent ---");
let indented_text = "    line 1\n    line 2\n        line 3";
println(`Original:\n${indented_text}`);
let dedented = dedent(indented_text);
println(`Dedented:\n${dedented}`);  // Should output: "line 1\nline 2\n    line 3"

// Example 2: prefix
println("\n--- Example 2: prefix ---");
let text = "line 1\nline 2\nline 3";
println(`Original:\n${text}`);
let prefixed = prefix(text, "    ");
println(`Prefixed:\n${prefixed}`);  // Should output: "    line 1\n    line 2\n    line 3"

// Return success message
"Text tools example completed successfully!"