// write_read.rhai
// Demonstrates writing content to and reading content from a container
// using the write_content and read_content methods

println("Starting write/read container example...");

// Define image and container names
let base_image = "ubuntu:22.04";
let container_name = "write-read-demo";
let final_image_name = "write-read-demo:latest";

println(`Creating container '${container_name}' from base image '${base_image}'...`);

// Create a new buildah container
let builder = bah_new(container_name, base_image);

// Update package lists
println("Updating package lists...");
let update_result = builder.run("apt-get update -y");
println(`Package update result: ${update_result.success ? "Success" : "Failed"}`);

// Write a simple text file to the container
println("\nWriting content to the container...");
let text_content = "This is a test file created using write_content.\nIt supports multiple lines.\n";
let write_result = builder.write_content(text_content, "/test.txt");
println(`Write result: ${write_result.success ? "Success" : "Failed"}`);

// Write a simple HTML file to the container
println("\nWriting HTML content to the container...");
let html_content = `
<!DOCTYPE html>
<html>
<head>
    <title>Write Content Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0066cc;
        }
    </style>
</head>
<body>
    <h1>Hello from Buildah!</h1>
    <p>This HTML file was created using the write_content method.</p>
</body>
</html>
`;
let html_write_result = builder.write_content(html_content, "/var/www/html/index.html");
println(`HTML write result: ${html_write_result.success ? "Success" : "Failed"}`);

// Write a simple shell script to the container
println("\nWriting shell script to the container...");
let script_content = `
#!/bin/bash
echo "This script was created using write_content"
echo "Current directory: $(pwd)"
echo "Files in current directory:"
ls -la
`;
let script_write_result = builder.write_content(script_content, "/test.sh");
println(`Script write result: ${script_write_result.success ? "Success" : "Failed"}`);

// Make the script executable
builder.run("chmod +x /test.sh");

// Read back the content we wrote
println("\nReading content from the container...");
let read_text = builder.read_content("/test.txt");
println("Text file content:");
println(read_text);

let read_html = builder.read_content("/var/www/html/index.html");
println("\nHTML file content (first 100 characters):");
println(read_html.substr(0, 100) + "...");

let read_script = builder.read_content("/test.sh");
println("\nScript file content:");
println(read_script);

// Execute the script we created
println("\nExecuting the script we created...");
let script_result = builder.run("/test.sh");
println("Script output:");
println(script_result.stdout);

// Commit the container to an image
println(`\nCommitting container to image '${final_image_name}'...`);
let commit_result = builder.commit(final_image_name);
println(`Commit result: ${commit_result.success ? "Success" : "Failed"}`);

// Clean up the buildah container
println("Cleaning up buildah container...");
builder.remove();

println("\nWrite/read example completed successfully!");

"Write/read example completed successfully!"