// 01_hello_world.rhai
// A simple hello world script to demonstrate basic Rhai functionality

// Print a message
println("Hello from <PERSON><PERSON>!");

// Define a function
fn greet(name) {
    "Hello, " + name + "!"
}

// Call the function and print the result
let greeting = greet("SAL User");
println(greeting);

// Do some basic calculations
let a = 5;
let b = 7;
println(`${a} + ${b} = ${a + b}`);
println(`${a} * ${b} = ${a * b}`);

// Create and use an array
let numbers = [1, 2, 3, 4, 5];
println("Numbers: " + numbers);
println("Sum of numbers: " + numbers.reduce(|sum, n| sum + n, 0));

// Create and use a map
let person = #{
    name: "<PERSON>",
    age: 30,
    occupation: "Developer"
};

println("Person: " + person);
println("Name: " + person.name);
println("Age: " + person.age);

// Return a success message
"Hello world script completed successfully!"