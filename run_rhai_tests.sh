#!/bin/bash
# run_rhai_tests.sh
# Script to run all Rhai tests in the rhai_tests directory

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create log file
LOG_FILE="run_rhai_tests.log"
> $LOG_FILE  # Clear log file if it exists

# Function to log messages to both console and log file
log() {
    echo -e "$1" | tee -a $LOG_FILE
}

# Print header
log "${BLUE}=======================================${NC}"
log "${BLUE}      Running All Rhai Tests          ${NC}"
log "${BLUE}=======================================${NC}"

# Find all test runner scripts
RUNNERS=$(find rhai_tests -name "run_all_tests.rhai")

# Initialize counters
TOTAL_MODULES=0
PASSED_MODULES=0
FAILED_MODULES=0

# Run each test runner
for runner in $RUNNERS; do
    # Extract module name from path
    module=$(echo $runner | cut -d'/' -f3)

    log "\n${YELLOW}Running tests for module: ${module}${NC}"
    log "${YELLOW}-------------------------------------${NC}"

    # Run the test runner
    herodo --path $runner | tee -a $LOG_FILE
    TEST_RESULT=${PIPESTATUS[0]}

    # Check if the test passed
    if [ $TEST_RESULT -eq 0 ]; then
        log "${GREEN}✓ Module ${module} tests passed${NC}"
        PASSED_MODULES=$((PASSED_MODULES + 1))
    else
        log "${RED}✗ Module ${module} tests failed${NC}"
        FAILED_MODULES=$((FAILED_MODULES + 1))
    fi

    TOTAL_MODULES=$((TOTAL_MODULES + 1))
done

# Print summary
log "\n${BLUE}=======================================${NC}"
log "${BLUE}            Test Summary              ${NC}"
log "${BLUE}=======================================${NC}"
log "Total modules tested: ${TOTAL_MODULES}"
log "Passed: ${GREEN}${PASSED_MODULES}${NC}"
log "Failed: ${RED}${FAILED_MODULES}${NC}"

# Set exit code based on test results
if [ $FAILED_MODULES -eq 0 ]; then
    log "\n${GREEN}All tests passed!${NC}"
    exit 0
else
    log "\n${RED}Some tests failed!${NC}"
    exit 1
fi
