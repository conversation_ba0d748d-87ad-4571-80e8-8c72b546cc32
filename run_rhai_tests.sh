#!/bin/bash
# run_rhai_tests.sh
# Script to run all Rhai tests in the rhai_tests directory

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create log file
LOG_FILE="run_rhai_tests.log"
> $LOG_FILE  # Clear log file if it exists

# Function to log messages to both console and log file
log() {
    echo -e "$1" | tee -a $LOG_FILE
}

# Function to setup Zinit daemon for zinit_client tests
setup_zinit() {
    log "${YELLOW}Setting up Zinit daemon for zinit_client tests...${NC}"

    # Check if Zinit is available
    if ! command -v zinit &> /dev/null; then
        log "${YELLOW}Zinit not found, zinit_client tests will be skipped${NC}"
        return 1
    fi

    # Check if Zinit daemon is already running
    if [ -S "/tmp/zinit.sock" ]; then
        log "${YELLOW}Zinit daemon already running${NC}"
        return 0
    fi

    # Start Zinit daemon in background
    log "${YELLOW}Starting Zinit daemon...${NC}"
    sudo nohup zinit --socket /tmp/zinit.sock init > /tmp/zinit.log 2>&1 &
    ZINIT_PID=$!

    # Wait for socket to be created
    for i in {1..10}; do
        if [ -S "/tmp/zinit.sock" ]; then
            # Set permissions so tests can access the socket
            sudo chmod 666 /tmp/zinit.sock
            log "${GREEN}✓ Zinit daemon started successfully${NC}"
            return 0
        fi
        sleep 1
    done

    log "${RED}✗ Failed to start Zinit daemon${NC}"
    return 1
}

# Function to cleanup Zinit daemon
cleanup_zinit() {
    log "${YELLOW}Cleaning up Zinit daemon...${NC}"
    sudo pkill -f "zinit.*init" 2>/dev/null || true
    rm -f /tmp/zinit.sock 2>/dev/null || true
    log "${GREEN}✓ Zinit daemon cleaned up${NC}"
}

# Print header
log "${BLUE}=======================================${NC}"
log "${BLUE}      Running All Rhai Tests          ${NC}"
log "${BLUE}=======================================${NC}"

# Find all test runner scripts
RUNNERS=$(find rhai_tests -name "run_all_tests.rhai")

# Initialize counters
TOTAL_MODULES=0
PASSED_MODULES=0
FAILED_MODULES=0

# Run each test runner
for runner in $RUNNERS; do
    # Extract module name from path
    module=$(echo $runner | cut -d'/' -f3)

    log "\n${YELLOW}Running tests for module: ${module}${NC}"
    log "${YELLOW}-------------------------------------${NC}"

    # Run the test runner
    herodo --path $runner | tee -a $LOG_FILE
    TEST_RESULT=${PIPESTATUS[0]}

    # Check if the test passed
    if [ $TEST_RESULT -eq 0 ]; then
        log "${GREEN}✓ Module ${module} tests passed${NC}"
        PASSED_MODULES=$((PASSED_MODULES + 1))
    else
        log "${RED}✗ Module ${module} tests failed${NC}"
        FAILED_MODULES=$((FAILED_MODULES + 1))
    fi

    TOTAL_MODULES=$((TOTAL_MODULES + 1))
done

# Print summary
log "\n${BLUE}=======================================${NC}"
log "${BLUE}            Test Summary              ${NC}"
log "${BLUE}=======================================${NC}"
log "Total modules tested: ${TOTAL_MODULES}"
log "Passed: ${GREEN}${PASSED_MODULES}${NC}"
log "Failed: ${RED}${FAILED_MODULES}${NC}"

# Set exit code based on test results
if [ $FAILED_MODULES -eq 0 ]; then
    log "\n${GREEN}All tests passed!${NC}"
    exit 0
else
    log "\n${RED}Some tests failed!${NC}"
    exit 1
fi
